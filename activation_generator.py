#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import hashlib
import base64
import time
import hmac
import argparse
import sys
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

class ActivationCodeSystem:
    """
    确定性激活码生成与验证系统
    使用AES加密保护激活码内容
    """
    
    def __init__(self, secret_key=None):
        # 使用固定密钥，确保相同输入产生相同输出
        self.secret_key = secret_key or "nukita_pvv_secure_activation_key_2023"
        
    def generate_activation_code(self, machine_code, valid_days=365):
        """生成加密的激活码"""
        try:
            # 验证机器码
            if not machine_code or len(machine_code) < 16:
                raise ValueError("机器码无效或长度不足")
            
            # 标准化机器码 - 取前16位作为标识符
            machine_id = machine_code[:16]
            
            # 计算过期时间 (以天为单位，避免秒级差异)
            # 获取当前日期（忽略时分秒，只保留年月日）
            current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            # 计算过期日期（精确到天）
            expiry_date = current_date + timedelta(days=valid_days)
            # 转换为时间戳
            expires_at = int(expiry_date.timestamp())
            
            # 构建激活数据
            activation_data = f"{machine_id}:{expires_at}"
            
            # 从密钥派生AES加密密钥和HMAC密钥
            key_material = hashlib.sha256(self.secret_key.encode()).digest()
            aes_key = key_material[:16]  # 取前16字节作为AES密钥
            hmac_key = key_material[16:] # 取后16字节作为HMAC密钥
            
            # 生成随机初始化向量
            iv = get_random_bytes(16)
            
            # 使用AES-CBC模式加密数据
            cipher = AES.new(aes_key, AES.MODE_CBC, iv)
            encrypted_data = cipher.encrypt(pad(activation_data.encode(), AES.block_size))
            
            # 组合IV和加密数据
            encrypted_package = iv + encrypted_data
            
            # 为整个加密包计算HMAC签名
            signature = hmac.new(
                hmac_key,
                encrypted_package,
                digestmod=hashlib.sha256
            ).digest()[:16]  # 使用16字节的签名
            
            # 最终激活码：IV + 加密数据 + 签名
            final_package = encrypted_package + signature
            
            # Base64编码，确保可移植性
            activation_code = base64.urlsafe_b64encode(final_package).decode()
            
            return {
                'success': True,
                'activation_code': activation_code,
                'machine_id': machine_id,
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_activation_code(self, machine_code, activation_code):
        """验证加密激活码的有效性"""
        try:
            # 解码激活码
            try:
                decoded_bytes = base64.urlsafe_b64decode(activation_code)
            except:
                return {'valid': False, 'message': '激活码格式无效或已损坏'}
            
            # 检查长度，至少需要IV(16字节) + AES块(至少16字节) + HMAC签名(16字节)
            if len(decoded_bytes) < 48:
                return {'valid': False, 'message': '激活码长度不足'}
            
            # 从密钥派生AES加密密钥和HMAC密钥
            key_material = hashlib.sha256(self.secret_key.encode()).digest()
            aes_key = key_material[:16]  # 取前16字节作为AES密钥
            hmac_key = key_material[16:] # 取后16字节作为HMAC密钥
            
            # 分解激活码
            iv = decoded_bytes[:16]
            signature = decoded_bytes[-16:]  # 最后16字节是签名
            encrypted_data = decoded_bytes[16:-16]  # 中间部分是加密数据
            
            # 验证HMAC签名
            expected_signature = hmac.new(
                hmac_key,
                decoded_bytes[:-16],  # IV + 加密数据
                digestmod=hashlib.sha256
            ).digest()[:16]
            
            if not hmac.compare_digest(signature, expected_signature):
                return {'valid': False, 'message': '激活码已被篡改或无效'}
            
            # 解密数据
            try:
                cipher = AES.new(aes_key, AES.MODE_CBC, iv)
                decrypted_padded = cipher.decrypt(encrypted_data)
                decrypted_data = unpad(decrypted_padded, AES.block_size).decode()
                
                # 分解解密后的数据
                parts = decrypted_data.split(':')
                if len(parts) != 2:
                    return {'valid': False, 'message': '激活码内容格式无效'}
                
                machine_id, expires_at_str = parts
            except Exception as e:
                return {'valid': False, 'message': f'激活码解密失败: {str(e)}'}
            
            # 验证机器码 - 修改为与UserManager一致的灵活匹配方式
            if machine_id != machine_code[:16]:
                print(f"机器码不完全匹配: 激活码={machine_id}, 当前={machine_code[:16]}")
                
                # 计算匹配度 - 如果匹配超过75%，仍然允许激活
                match_count = sum(a == b for a, b in zip(machine_id, machine_code[:16]))
                match_percent = (match_count / 16) * 100
                
                if match_percent >= 75:
                    print(f"机器码匹配度 {match_percent:.1f}% - 在允许范围内，继续验证")
                else:
                    return {'valid': False, 'message': '激活码与当前机器不匹配'}
            
            # 验证过期时间
            try:
                expires_at = int(expires_at_str)
                current_time = int(time.time())
            except:
                return {'valid': False, 'message': '激活码中的时间戳无效'}
            
            if current_time > expires_at:
                days_expired = (current_time - expires_at) // (24 * 60 * 60)
                return {'valid': False, 'message': f'激活码已过期 {days_expired} 天'}
            
            # 计算剩余有效天数
            days_remaining = (expires_at - current_time) // (24 * 60 * 60)
            
            return {
                'valid': True,
                'message': f'激活码有效，剩余 {days_remaining} 天',
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d'),
                'days_remaining': days_remaining
            }
            
        except Exception as e:
            return {'valid': False, 'message': f'验证失败: {str(e)}'}

def main():
    # 命令行界面实现
    parser = argparse.ArgumentParser(description='Nukita PVV 确定性激活码生成器')
    parser.add_argument('action', choices=['generate', 'verify', 'gui'], 
                       help='操作类型: generate(生成), verify(验证), gui(启动图形界面)')
    parser.add_argument('--machine-code', help='机器码')
    parser.add_argument('--activation-code', help='验证时使用的激活码')
    parser.add_argument('--days', type=int, default=365, help='激活码有效天数 (默认: 365)')
    parser.add_argument('--secret', help='自定义激活密钥')
    
    # 如果没有参数，默认启动GUI
    if len(sys.argv) == 1:
        sys.argv.append('gui')
    
    args = parser.parse_args()
    
    # 创建激活系统
    activation_system = ActivationCodeSystem(args.secret)
    
    if args.action == 'gui':
        # 启动图形界面
        launch_gui()
        return
    
    elif args.action == 'generate':
        if not args.machine_code:
            print("生成激活码时必须提供 --machine-code 参数")
            sys.exit(1)
            
        result = activation_system.generate_activation_code(args.machine_code, args.days)
        if result['success']:
            print(f"生成的激活码: {result['activation_code']}")
            print(f"过期时间: {result['expires_at_formatted']}")
        else:
            print(f"生成激活码失败: {result['error']}")
            sys.exit(1)
    
    elif args.action == 'verify':
        if not args.machine_code or not args.activation_code:
            print("验证激活码时必须提供 --machine-code 和 --activation-code 参数")
            sys.exit(1)
            
        result = activation_system.verify_activation_code(args.machine_code, args.activation_code)
        if result['valid']:
            print(f"激活码验证成功: {result['message']}")
            print(f"过期时间: {result['expires_at_formatted']}")
        else:
            print(f"激活码验证失败: {result['message']}")
            sys.exit(1)

def launch_gui():
    # 创建主窗口
    root = tk.Tk()
    root.title("Nukita PVV 激活码生成工具")
    root.geometry("680x520")
    root.configure(padx=10, pady=10)
    
    # 设置应用图标 (如果有)
    try:
        icon_path = os.path.join(os.path.dirname(__file__), "assets", "icon.ico")
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
    except:
        pass  # 忽略图标设置错误
    
    # 定义样式
    style = ttk.Style()
    style.configure("TButton", padding=6, relief="flat", background="#2196F3")
    style.configure("Generate.TButton", foreground="#000", background="#4CAF50", font=("Arial", 10, "bold"))
    style.configure("Copy.TButton", background="#FFC107")
    style.configure("Verify.TButton", background="#2196F3")
    
    # 主框架
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_frame = ttk.Frame(main_frame)
    title_frame.pack(fill=tk.X, pady=(0, 15))
    
    # 尝试加载和显示图标
    try:
        logo_path = os.path.join(os.path.dirname(__file__), "assets", "logo.png")
        if os.path.exists(logo_path):
            from PIL import Image, ImageTk
            logo = Image.open(logo_path)
            logo = logo.resize((48, 48), Image.LANCZOS)
            logo_img = ImageTk.PhotoImage(logo)
            logo_label = ttk.Label(title_frame, image=logo_img)
            logo_label.image = logo_img  # 保持引用防止垃圾回收
            logo_label.pack(side=tk.LEFT, padx=(0, 10))
    except:
        pass  # 忽略图像加载错误
    
    title_label = ttk.Label(title_frame, text="Nukita PVV 激活码生成工具", font=("Arial", 16, "bold"))
    title_label.pack(side=tk.LEFT, pady=10)
    
    # 版本号
    version_label = ttk.Label(title_frame, text="v1.0.0", font=("Arial", 8))
    version_label.pack(side=tk.RIGHT, padx=5, pady=10)
    
    # 机器码输入框架
    input_frame = ttk.LabelFrame(main_frame, text="输入信息", padding=10)
    input_frame.pack(fill=tk.X, pady=10)
    
    # 机器码
    machine_code_frame = ttk.Frame(input_frame)
    machine_code_frame.pack(fill=tk.X, pady=5)
    ttk.Label(machine_code_frame, text="机器码:", width=10).pack(side=tk.LEFT)
    machine_code_entry = ttk.Entry(machine_code_frame)
    machine_code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
    
    # 导入机器码按钮
    def import_machine_code():
        try:
            code = root.clipboard_get().strip()
            if code and len(code) >= 16:
                machine_code_entry.delete(0, tk.END)
                machine_code_entry.insert(0, code)
                messagebox.showinfo("导入成功", "已从剪贴板导入机器码")
            else:
                messagebox.showwarning("导入失败", "剪贴板中没有有效的机器码")
        except:
            messagebox.showwarning("导入失败", "无法从剪贴板获取内容")
    
    import_button = ttk.Button(machine_code_frame, text="粘贴", command=import_machine_code)
    import_button.pack(side=tk.LEFT, padx=2)
    
    # 有效期
    days_frame = ttk.Frame(input_frame)
    days_frame.pack(fill=tk.X, pady=5)
    ttk.Label(days_frame, text="有效期(天):", width=10).pack(side=tk.LEFT)
    
    days_var = tk.StringVar(value="365")
    days_entry = ttk.Entry(days_frame, width=10, textvariable=days_var)
    days_entry.pack(side=tk.LEFT, padx=5)
    
    # 快速选择有效期
    def set_days(days):
        days_var.set(str(days))
    
    ttk.Button(days_frame, text="30天", width=6, command=lambda: set_days(30)).pack(side=tk.LEFT, padx=2)
    ttk.Button(days_frame, text="90天", width=6, command=lambda: set_days(90)).pack(side=tk.LEFT, padx=2)
    ttk.Button(days_frame, text="1年", width=6, command=lambda: set_days(365)).pack(side=tk.LEFT, padx=2)
    ttk.Button(days_frame, text="永久", width=6, command=lambda: set_days(3650)).pack(side=tk.LEFT, padx=2)
    
    # 显示调试信息复选框
    show_debug = tk.BooleanVar(value=False)
    debug_check = ttk.Checkbutton(days_frame, text="显示调试信息", variable=show_debug)
    debug_check.pack(side=tk.LEFT, padx=20)
    
    # 生成按钮框架
    generate_frame = ttk.Frame(main_frame)
    generate_frame.pack(fill=tk.X, pady=10)
    
    # 使用大号生成按钮，突出显示
    generate_button = ttk.Button(
        generate_frame, 
        text="生成激活码", 
        style="Generate.TButton",
        command=lambda: generate_activation()
    )
    generate_button.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True, ipady=5)
    
    # 激活码显示框架 - 初始不显示
    activation_frame = ttk.Frame(main_frame)
    activation_frame_visible = tk.BooleanVar(value=False)
    
    activation_code_label = ttk.Label(activation_frame, text="激活码:")
    activation_code_label.pack(side=tk.LEFT)
    
    activation_code_var = tk.StringVar()
    activation_code_entry = ttk.Entry(activation_frame, textvariable=activation_code_var, width=40)
    activation_code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
    
    verify_button = ttk.Button(
        activation_frame, 
        text="验证", 
        style="Verify.TButton",
        command=lambda: verify_activation()
    )
    verify_button.pack(side=tk.LEFT, padx=2)
    
    copy_button = ttk.Button(
        activation_frame, 
        text="复制", 
        style="Copy.TButton",
        command=lambda: copy_activation()
    )
    copy_button.pack(side=tk.LEFT, padx=2)
    
    # 结果显示
    result_frame = ttk.LabelFrame(main_frame, text="结果")
    result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    result_text = tk.Text(result_frame, height=10, wrap=tk.WORD, font=("Consolas", 10))
    result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(result_text, orient="vertical", command=result_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    result_text.configure(yscrollcommand=scrollbar.set)
    
    # 初始化结果显示文本
    result_text.insert(tk.END, "使用说明:\n\n")
    result_text.insert(tk.END, "1. 输入客户提供的机器码\n")
    result_text.insert(tk.END, "2. 设置激活码有效期（默认365天）\n")
    result_text.insert(tk.END, "3. 点击「生成激活码」按钮\n")
    result_text.insert(tk.END, "4. 复制生成的激活码发送给客户\n")
    result_text.insert(tk.END, "5. 可以使用「验证」按钮测试激活码是否有效\n\n")
    result_text.insert(tk.END, "如有问题，请勾选「显示调试信息」查看详情")
    
    # 生成激活码
    def generate_activation():
        machine_code = machine_code_entry.get().strip()
        
        if not machine_code:
            messagebox.showerror("错误", "请输入机器码")
            return
        
        try:
            days = int(days_var.get())
            if days <= 0:
                raise ValueError("有效期必须为正数")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的天数")
            return
        
        generator = ActivationCodeSystem()
        result = generator.generate_activation_code(machine_code, days)
        
        if result['success']:
            # 生成成功
            code = result['activation_code']
            expires = result['expires_at_formatted']
            
            # 设置激活码
            activation_code_var.set(code)
            
            # 如果激活码区域还未显示，则显示
            if not activation_frame_visible.get():
                activation_frame.pack(fill=tk.X, pady=10, after=generate_frame)
                activation_frame_visible.set(True)
            
            # 显示信息
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, f"激活码已生成\n\n")
            result_text.insert(tk.END, f"过期时间: {expires}\n")
            result_text.insert(tk.END, f"有效期: {days} 天\n\n")
            
            # 显示调试信息
            if show_debug.get():
                result_text.insert(tk.END, f"原始机器码: {machine_code}\n")
                result_text.insert(tk.END, f"机器ID: {result['machine_id']}\n")
                result_text.insert(tk.END, f"时间戳: {result['expires_at']}\n")
            
            # 自动选中激活码便于复制
            activation_code_entry.focus_set()
            activation_code_entry.select_range(0, tk.END)
            
            messagebox.showinfo("成功", "激活码已生成，请复制")
        else:
            # 生成失败
            error_msg = result['error']
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, f"生成失败: {error_msg}\n\n")
            
            messagebox.showerror("错误", error_msg)
    
    # 验证激活码
    def verify_activation():
        machine_code = machine_code_entry.get().strip()
        activation_code = activation_code_var.get().strip()
        
        if not machine_code or not activation_code:
            messagebox.showerror("错误", "请输入机器码和激活码")
            return
        
        generator = ActivationCodeSystem()
        result = generator.verify_activation_code(machine_code, activation_code)
        
        result_text.delete(1.0, tk.END)
        
        if result['valid']:
            # 验证成功
            expires_at = result['expires_at']
            expires_date = result['expires_at_formatted']
            days_remaining = result['days_remaining']
            
            result_text.insert(tk.END, "验证成功\n\n")
            result_text.insert(tk.END, f"激活码有效\n")
            result_text.insert(tk.END, f"过期时间: {expires_date}\n")
            result_text.insert(tk.END, f"剩余天数: {days_remaining} 天\n")
            
            # 显示调试信息
            if show_debug.get():
                try:
                    decoded_bytes = base64.urlsafe_b64decode(activation_code)
                    result_text.insert(tk.END, f"\n解码信息:\n")
                    result_text.insert(tk.END, f"激活码总长度: {len(decoded_bytes)} 字节\n")
                    result_text.insert(tk.END, f"IV: {decoded_bytes[:16].hex()[:16]}...\n")
                    result_text.insert(tk.END, f"加密数据长度: {len(decoded_bytes[16:-16])} 字节\n")
                    result_text.insert(tk.END, f"签名: {decoded_bytes[-16:].hex()[:16]}...\n")
                    result_text.insert(tk.END, f"机器ID (已解密): {machine_code[:16]}\n")
                except:
                    result_text.insert(tk.END, f"\n无法解码激活码\n")
            
            messagebox.showinfo("验证成功", f"激活码有效，剩余 {days_remaining} 天")
        else:
            # 验证失败
            message = result['message']
            result_text.insert(tk.END, f"验证失败: {message}\n\n")
            
            # 显示调试信息
            if show_debug.get():
                try:
                    decoded_bytes = base64.urlsafe_b64decode(activation_code)
                    result_text.insert(tk.END, f"解码信息:\n")
                    result_text.insert(tk.END, f"激活码总长度: {len(decoded_bytes)} 字节\n")
                    
                    if len(decoded_bytes) >= 48:  # 至少包含 IV + 最小块大小 + 签名
                        result_text.insert(tk.END, f"IV: {decoded_bytes[:16].hex()[:16]}...\n")
                        result_text.insert(tk.END, f"加密数据长度: {len(decoded_bytes[16:-16])} 字节\n")
                        result_text.insert(tk.END, f"签名: {decoded_bytes[-16:].hex()[:16]}...\n")
                    
                    # 尝试判断是否是老格式的激活码
                    try:
                        old_format = base64.urlsafe_b64decode(activation_code).decode()
                        if ":" in old_format and len(old_format.split(":")) == 3:
                            result_text.insert(tk.END, f"\n检测到旧格式激活码，无法使用新验证算法\n")
                    except:
                        pass
                    
                    # 检查机器码匹配情况
                    result_text.insert(tk.END, f"\n机器码信息:\n")
                    result_text.insert(tk.END, f"本机码: {machine_code[:16]}\n")
                except:
                    result_text.insert(tk.END, f"无法解码激活码\n")
            
            messagebox.showerror("验证失败", message)
    
    # 复制激活码
    def copy_activation():
        """复制激活码到剪贴板"""
        code = activation_code_var.get()
        if not code:
            return
        
        # 执行复制
        try:
            root.clipboard_clear()
            root.clipboard_append(code)
            messagebox.showinfo("复制成功", "激活码已复制到剪贴板")
        except Exception as e:
            messagebox.showinfo("复制", "请手动按 Ctrl+C 复制已选中的激活码")
    
    # 添加状态栏
    status_frame = ttk.Frame(root)
    status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))
    
    status_label = ttk.Label(status_frame, text="就绪", anchor=tk.W)
    status_label.pack(side=tk.LEFT)
    
    copyright_label = ttk.Label(status_frame, text="© 2023 Nukita PVV", anchor=tk.E)
    copyright_label.pack(side=tk.RIGHT)
    
    # 绑定回车键
    def on_enter(event):
        widget = root.focus_get()
        if widget == machine_code_entry or widget == days_entry:
            generate_activation()
        elif widget == activation_code_entry:
            verify_activation()
    
    root.bind("<Return>", on_enter)
    
    # 启动应用
    root.mainloop()

if __name__ == "__main__":
    main() 