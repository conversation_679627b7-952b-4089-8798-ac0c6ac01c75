<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1a0c1767-8230-4eb4-bf8c-b647b09d6c9f" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/PVV.py" beforeDir="false" afterPath="$PROJECT_DIR$/PVV.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/statics/.integrity" beforeDir="false" afterPath="$PROJECT_DIR$/statics/.integrity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/statics/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/statics/index.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/frontend/package.json" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2uJC4RGoMhqREpWyty7VS4iDRBP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;ASKED_MARK_IGNORED_FILES_AS_EXCLUDED&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;webview__version&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/project/go/pvv/backup/chat&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.31494254&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;http.proxy&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\soft\\JetBrains\\IntelliJ IDEA 2023.2.1\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\project\go\pvv\backup\chat" />
      <recent name="D:\project\go\pvv" />
      <recent name="D:\project\go\pvv\out" />
      <recent name="D:\project\go\pvv\frontend\src\constants" />
      <recent name="D:\project\go\pvv\backup\config" />
    </key>
  </component>
  <component name="RunManager" selected="Python.dev">
    <configuration name="PVV (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pvv" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/PVV.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="activation_generator" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pvv" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/activation_generator.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="build_integrity_code" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pvv" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/build_integrity_code.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pvv" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/dev.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="vite_build" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pvv" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/vite_build.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.dev" />
        <item itemvalue="Python.PVV (1)" />
        <item itemvalue="Python.vite_build" />
        <item itemvalue="Python.build_integrity_code" />
        <item itemvalue="Python.activation_generator" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1a0c1767-8230-4eb4-bf8c-b647b09d6c9f" name="Changes" comment="" />
      <created>1741957100736</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741957100736</updated>
      <workItem from="1741957101798" duration="961000" />
      <workItem from="1741958084828" duration="1890000" />
      <workItem from="1741960000457" duration="2575000" />
      <workItem from="1741962763233" duration="2236000" />
      <workItem from="1741965243083" duration="703000" />
      <workItem from="1742038460127" duration="4521000" />
      <workItem from="1742047526273" duration="266000" />
      <workItem from="1742048360690" duration="176000" />
      <workItem from="1742097879053" duration="17200000" />
      <workItem from="1742273814376" duration="292000" />
      <workItem from="1742274163495" duration="972000" />
      <workItem from="1742275430837" duration="215000" />
      <workItem from="1742373514061" duration="751000" />
      <workItem from="1742374507136" duration="996000" />
      <workItem from="1742385014311" duration="707000" />
      <workItem from="1742386516225" duration="195000" />
      <workItem from="1742387118632" duration="1279000" />
      <workItem from="1742563007853" duration="32000" />
      <workItem from="1742611970855" duration="26788000" />
      <workItem from="1742651816010" duration="588000" />
      <workItem from="1742653022575" duration="3720000" />
      <workItem from="1742661007261" duration="2362000" />
      <workItem from="1742696676136" duration="21927000" />
      <workItem from="1742782903444" duration="10965000" />
      <workItem from="1742795864304" duration="3878000" />
      <workItem from="1742799889867" duration="23389000" />
      <workItem from="1742830922384" duration="494000" />
      <workItem from="1742832237931" duration="210000" />
      <workItem from="1742870379108" duration="34000" />
      <workItem from="1742873425816" duration="3467000" />
      <workItem from="1742880478211" duration="645000" />
      <workItem from="1742890377194" duration="908000" />
      <workItem from="1742891451527" duration="69000" />
      <workItem from="1742892356659" duration="283000" />
      <workItem from="1742893285817" duration="1076000" />
      <workItem from="1742895982385" duration="18422000" />
      <workItem from="1742952040228" duration="1976000" />
      <workItem from="1742954589905" duration="46000" />
      <workItem from="1742978441805" duration="7950000" />
      <workItem from="1742993591348" duration="79000" />
      <workItem from="1742993987808" duration="717000" />
      <workItem from="1743001062928" duration="2390000" />
      <workItem from="1743005030521" duration="171000" />
      <workItem from="1743012815694" duration="6432000" />
      <workItem from="1743043012431" duration="7466000" />
      <workItem from="1743050763148" duration="404000" />
      <workItem from="1743053157254" duration="292000" />
      <workItem from="1743053632546" duration="352000" />
      <workItem from="1743081931786" duration="16229000" />
      <workItem from="1743133390129" duration="11610000" />
      <workItem from="1743145160405" duration="229000" />
      <workItem from="1743145873732" duration="3384000" />
      <workItem from="1743152316208" duration="1562000" />
      <workItem from="1743161883802" duration="2056000" />
      <workItem from="1743169370676" duration="1491000" />
      <workItem from="1743170888856" duration="138000" />
      <workItem from="1743171501138" duration="5167000" />
      <workItem from="1743177603350" duration="3746000" />
      <workItem from="1743221922454" duration="20253000" />
      <workItem from="1743261395969" duration="7539000" />
      <workItem from="1743323627670" duration="27346000" />
      <workItem from="1743391226078" duration="21760000" />
      <workItem from="1743466718414" duration="8733000" />
      <workItem from="1743476138617" duration="12000" />
      <workItem from="1743476849628" duration="5673000" />
      <workItem from="1743482871224" duration="1643000" />
      <workItem from="1743484563699" duration="62000" />
      <workItem from="1743484744121" duration="39000" />
      <workItem from="1743489278275" duration="6739000" />
      <workItem from="1743680001436" duration="63000" />
      <workItem from="1744533655515" duration="41000" />
      <workItem from="1745112549483" duration="3111000" />
      <workItem from="1745120107664" duration="783000" />
      <workItem from="1746932025576" duration="54000" />
      <workItem from="1747105795635" duration="3328000" />
      <workItem from="1747361180368" duration="5332000" />
      <workItem from="1748401617708" duration="36000" />
      <workItem from="1748401664195" duration="9935000" />
      <workItem from="1748427318273" duration="936000" />
      <workItem from="1748435521801" duration="1375000" />
      <workItem from="1748457429443" duration="26000" />
      <workItem from="1748518516500" duration="2497000" />
      <workItem from="1748522091317" duration="3848000" />
      <workItem from="1748527804900" duration="3070000" />
      <workItem from="1748530910011" duration="10204000" />
      <workItem from="1748572595174" duration="6370000" />
      <workItem from="1748585542175" duration="3916000" />
      <workItem from="1748669587264" duration="14820000" />
      <workItem from="1748689038521" duration="13922000" />
      <workItem from="1748705853733" duration="9036000" />
      <workItem from="1748744734639" duration="21178000" />
      <workItem from="1748838064733" duration="25426000" />
      <workItem from="1748920153170" duration="1908000" />
      <workItem from="1748924430512" duration="13567000" />
      <workItem from="1748952758980" duration="2604000" />
      <workItem from="1748965342185" duration="25000" />
      <workItem from="1748965437279" duration="1487000" />
      <workItem from="1748969424157" duration="752000" />
      <workItem from="1748972809369" duration="152000" />
      <workItem from="1749007726464" duration="28000" />
      <workItem from="1749008840877" duration="3920000" />
      <workItem from="1749091527953" duration="13913000" />
      <workItem from="1749115832054" duration="1000" />
      <workItem from="1749116263014" duration="58000" />
      <workItem from="1749125425735" duration="6454000" />
      <workItem from="1749143055222" duration="257000" />
      <workItem from="1749143330663" duration="204000" />
      <workItem from="1749180048098" duration="1023000" />
      <workItem from="1749181137075" duration="23000" />
      <workItem from="1749203151423" duration="309000" />
      <workItem from="1749204163430" duration="2788000" />
      <workItem from="1749223369239" duration="2520000" />
      <workItem from="1749228760901" duration="288000" />
      <workItem from="1749352708337" duration="9165000" />
      <workItem from="1749371054195" duration="20000" />
      <workItem from="1749371371842" duration="285000" />
      <workItem from="1749373080424" duration="53000" />
      <workItem from="1749386902730" duration="3344000" />
      <workItem from="1749390891574" duration="296000" />
      <workItem from="1749391400394" duration="3644000" />
      <workItem from="1749396519004" duration="40000" />
      <workItem from="1749461169018" duration="897000" />
      <workItem from="1749462222535" duration="5509000" />
      <workItem from="1749661743395" duration="2802000" />
      <workItem from="1749665050319" duration="337000" />
      <workItem from="1749701890765" duration="2439000" />
      <workItem from="1749705012252" duration="4034000" />
      <workItem from="1749724449818" duration="486000" />
      <workItem from="1749724959611" duration="4546000" />
      <workItem from="1749785243771" duration="3852000" />
      <workItem from="1749789308598" duration="5387000" />
      <workItem from="1749815563671" duration="3659000" />
      <workItem from="1749820839187" duration="378000" />
      <workItem from="1749821488723" duration="218000" />
      <workItem from="1749822684313" duration="6869000" />
      <workItem from="1749836817535" duration="1154000" />
      <workItem from="1749869193894" duration="10609000" />
      <workItem from="1749884459548" duration="1923000" />
      <workItem from="1749951686739" duration="1755000" />
      <workItem from="1749954595823" duration="3592000" />
      <workItem from="1749958380453" duration="1071000" />
      <workItem from="1750475443506" duration="612000" />
      <workItem from="1751164096772" duration="1869000" />
      <workItem from="1751940156934" duration="11650000" />
      <workItem from="1751954855851" duration="912000" />
      <workItem from="1751957328515" duration="52000" />
      <workItem from="1751973590554" duration="9637000" />
      <workItem from="1751984966140" duration="635000" />
      <workItem from="1751985892591" duration="3343000" />
      <workItem from="1751990718793" duration="3581000" />
      <workItem from="1752064664996" duration="9543000" />
      <workItem from="1752115485221" duration="12565000" />
      <workItem from="1752166752778" duration="13088000" />
      <workItem from="1752211745619" duration="25581000" />
      <workItem from="1752288385554" duration="22363000" />
      <workItem from="1752376693054" duration="6369000" />
      <workItem from="1752385070994" duration="2052000" />
      <workItem from="1752414670034" duration="12544000" />
      <workItem from="1752456014052" duration="764000" />
      <workItem from="1752457948713" duration="688000" />
      <workItem from="1752504524895" duration="1544000" />
      <workItem from="1752545154728" duration="7063000" />
      <workItem from="1752562124010" duration="85000" />
      <workItem from="1752562838151" duration="2333000" />
      <workItem from="1752580448998" duration="6968000" />
      <workItem from="1752595538774" duration="5404000" />
      <workItem from="1752604013987" duration="1867000" />
      <workItem from="1752606072670" duration="47000" />
      <workItem from="1752606161294" duration="315000" />
      <workItem from="1752629765718" duration="1295000" />
      <workItem from="1752717335492" duration="4559000" />
      <workItem from="1752752225426" duration="12363000" />
      <workItem from="1752767180347" duration="885000" />
      <workItem from="1752769360114" duration="96000" />
      <workItem from="1752803558195" duration="3945000" />
      <workItem from="1752840216596" duration="5474000" />
      <workItem from="1752853320542" duration="355000" />
      <workItem from="1752854086278" duration="1444000" />
      <workItem from="1752886574852" duration="5590000" />
      <workItem from="1752979083492" duration="7510000" />
      <workItem from="1753002821864" duration="1637000" />
      <workItem from="1753004739248" duration="188000" />
      <workItem from="1753005160373" duration="1291000" />
      <workItem from="1753026364982" duration="1200000" />
      <workItem from="1753321274562" duration="6290000" />
      <workItem from="1753346587816" duration="5350000" />
      <workItem from="1753765142779" duration="605000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VgoProject">
    <integration-enabled>false</integration-enabled>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/backend/bridge/TimeValidator.py</url>
          <line>110</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/backend/bridge/TimeValidator.py</url>
          <line>62</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/backend/bridge/BookController.py</url>
          <line>2963</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/venv/Lib/site-packages/qtpy/__init__.py</url>
          <line>278</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/webview/platforms/qt.py</url>
          <line>45</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/webview/platforms/qt.py</url>
          <line>22</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/pvv$activation_generator.coverage" NAME="activation_generator Coverage Results" MODIFIED="1751946369489" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$dev.coverage" NAME="dev Coverage Results" MODIFIED="1753766041148" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$1.coverage" NAME="1 Coverage Results" MODIFIED="1749956586548" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$PVV__1_.coverage" NAME="PVV (1) Coverage Results" MODIFIED="1753351476129" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$vite_build__1_.coverage" NAME="vite_build (1) Coverage Results" MODIFIED="1743097683702" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$_vite_build__1_.coverage" NAME="第一步_vite_build (1) Coverage Results" MODIFIED="1743144439432" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$build_integrity_code.coverage" NAME="build_integrity_code Coverage Results" MODIFIED="1751954449093" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$copy_missing_modules.coverage" NAME="copy_missing_modules Coverage Results" MODIFIED="1743141310192" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$Runtime.coverage" NAME="Runtime Coverage Results" MODIFIED="1741957814584" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend/pvvruntime" />
    <SUITE FILE_PATH="coverage/pvv$build_test.coverage" NAME="build_test Coverage Results" MODIFIED="1743094045282" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$HardwareIdentifier.coverage" NAME="HardwareIdentifier Coverage Results" MODIFIED="1749785787802" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend/bridge" />
    <SUITE FILE_PATH="coverage/pvv$main.coverage" NAME="main Coverage Results" MODIFIED="1748672984154" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pvv$vite_build.coverage" NAME="vite_build Coverage Results" MODIFIED="1753350386185" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>