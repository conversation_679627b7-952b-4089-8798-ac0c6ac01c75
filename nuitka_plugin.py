# nuitka_plugin.py (Updated Version)
import os
from nuitka.plugins.PluginBase import NuitkaPluginBase

# 获取你修改后的 qt.py 的绝对路径
MODIFIED_QT_PY_PATH = os.path.abspath(
    os.path.join(os.path.dirname(__file__), 'webview', 'platforms', 'qt.py')
)

class MyPywebviewPlugin(NuitkaPluginBase):
    """
    这个插件强制 Nuitka 使用我们本地修改过的 webview.platforms.qt 模块。
    """
    plugin_name = "my-pywebview-plugin"

    # --- 主要修改在这里 ---
    # 旧签名: onModuleSourceCode(self, module_name, source_code)
    # 新签名: 增加 'source_filename' 参数来匹配新版 Nuitka 的 API
    def onModuleSourceCode(self, module_name, source_code, source_filename):
        """
        当 Nuitka 读取到一个模块的源码时，这个钩子会被调用。
        """
        print(module_name)
        # 我们只关心 'webview.platforms.qt' 这个模块
        if module_name == "webview.platforms.qt":
            self.info(f"Redirecting '{module_name}' to use local file: {MODIFIED_QT_PY_PATH}")

            # 读取我们自己修改过的文件的内容
            try:
                with open(MODIFIED_QT_PY_PATH, "r", encoding="utf-8") as f:
                    new_source_code = f.read()
                # 返回新的源码，Nuitka 将会用这个源码来编译
                return new_source_code
            except FileNotFoundError:
                self.sysexit(f"Error: Plugin could not find the modified qt.py at {MODIFIED_QT_PY_PATH}")

        # 对于其他模块，不做任何改动
        return source_code

# Nuitka 要求插件类必须命名为 NuitkaPluginDetector
class NuitkaPluginDetector(NuitkaPluginBase):
    plugin_name = MyPywebviewPlugin.plugin_name

    @staticmethod
    def isRelevant():
        return True

    def createPlugin(self):
        return MyPywebviewPlugin()