<template>
  <div class="search-panel-container" :class="{ visible: visible }">
    <div class="search-panel-header no-select">
      <div class="title">网络查询</div>
      <div class="actions">
        <el-button type="primary" @click="openSearchConfig" class="config-button" size="default">
          <el-icon class="config-icon"><Setting /></el-icon>
          <span>配置</span>
        </el-button>
        <el-button plain @click="$emit('close')" class="close-button">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <div class="fixed-content">
      <div class="search-input-container">
        <div class="input-row">
          <div class="search-wrapper" @mouseenter="showHistory = true" @mouseleave="showHistory = false">
            <el-input
              v-model="searchQuery"
              placeholder="输入搜索内容..."
              size="large"
              clearable
              @keyup.enter="performSearch"
              @focus="showHistory = true"
              class="search-input"
              ref="searchInputRef"
            >
              <template #append>
                <el-button @click="performSearch" :disabled="!searchQuery" type="primary">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
            
            <!-- 历史搜索下拉菜单 -->
            <div class="history-dropdown" v-show="showHistory && searchHistory.length > 0">
              <div class="dropdown-header no-select">
                <span>最近搜索</span>
                <el-button link @click.stop="clearHistory" size="small" type="danger">清空</el-button>
              </div>
              <div class="dropdown-list">
                <div 
                  v-for="(term, index) in searchHistory" 
                  :key="index"
                  class="history-item no-select"
                  @click="selectFromHistory(term)"
                >
                  <el-icon><Clock /></el-icon>
                  <span class="term-text">{{ term }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <el-button 
            v-if="searchProviders.length > 1"
            type="success" 
            @click="multiSearch" 
            :disabled="!searchQuery"
            class="multi-search-button"
          >
            <el-icon><Connection /></el-icon>
            多搜
          </el-button>
        </div>
      </div>
    </div>

    <div class="scroll-content">
      <div class="search-providers" v-if="searchProviders.length > 0">
        <div class="provider-list">
          <div 
            v-for="provider in searchProviders" 
            :key="provider.id" 
            class="provider-item"
          >
            <div class="provider-info">
              <div class="provider-name no-select">{{ provider.name }}</div>
              <div class="provider-url no-select">{{ formatUrl(provider.url) }}</div>
            </div>
            <div class="provider-actions">
              <el-button 
                type="primary" 
                @click="searchWithProvider(provider)" 
                :disabled="!searchQuery"
                class="open-button"
              >
                <el-icon><Link /></el-icon>
                打开
              </el-button>
            </div>
          </div>
        </div>

        <el-empty v-if="searchProviders.length === 0" description="暂无配置的搜索提供商" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { Search, Setting, Close, Link, Connection, Clock } from '@element-plus/icons-vue';
import { useConfigStore } from '@/stores/config';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bookId: {
    type: String,
    required: true
  },
  selectedText: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close', 'show-config']);
const configStore = useConfigStore();
const searchQuery = ref('');
const searchHistory = ref([]);
const MAX_HISTORY = 20;
const showSearchConfig = ref(false);
const searchInputRef = ref(null);
const showHistory = ref(false);

// Watch for selected text changes to automatically populate search query
watch(() => props.selectedText, (newText) => {
  if (newText && props.visible) {
    searchQuery.value = newText.trim();
  }
});

// Watch for visibility change to populate search query from selected text
watch(() => props.visible, (isVisible) => {
  if (isVisible && props.selectedText) {
    searchQuery.value = props.selectedText.trim();
  }
});

// 添加点击页面其他区域隐藏历史记录的功能
const handleClickOutside = (event) => {
  const wrapper = document.querySelector('.search-wrapper');
  if (wrapper && !wrapper.contains(event.target)) {
    showHistory.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  
  // 从本地存储加载搜索历史
  const savedHistory = localStorage.getItem('searchHistory');
  if (savedHistory) {
    try {
      searchHistory.value = JSON.parse(savedHistory);
    } catch (e) {
      console.error('解析搜索历史失败:', e);
    }
  }
});

// Get search providers from config store
const searchProviders = computed(() => {
  return configStore.search?.providers || [];
});

// Get default provider from config store
const getDefaultProvider = computed(() => {
  const providers = searchProviders.value;
  return providers.find(p => p.isDefault) || (providers.length > 0 ? providers[0] : null);
});

// Format URL for display (truncate if too long)
const formatUrl = (url) => {
  if (url.length > 40) {
    return url.substring(0, 37) + '...';
  }
  return url;
};

// 工具函数：构建搜索URL
const buildSearchUrl = (providerUrl, query) => {
  let url = providerUrl;
  
  // 空检查
  if (!url || !query) return url;
  
  // 处理不同的URL格式
  if (url.includes('{query}')) {
    // 使用占位符替换
    url = url.replace('{query}', encodeURIComponent(query));
  } else if (url.includes('{q}')) {
    // 有些搜索引擎使用{q}作为占位符
    url = url.replace('{q}', encodeURIComponent(query));
  } else {
    // 没有标准占位符，尝试判断搜索引擎类型
    const lowerUrl = url.toLowerCase();
    
    // 常见搜索引擎的参数名
    if (lowerUrl.includes('google') || lowerUrl.includes('duckduckgo')) {
      // Google, DuckDuckGo 使用 q 参数
      if (url.includes('?')) {
        url = `${url}&q=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?q=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('bing')) {
      // Bing使用q参数
      if (url.includes('?')) {
        url = `${url}&q=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?q=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('baidu')) {
      // 百度使用 wd 参数
      if (url.includes('?')) {
        url = `${url}&wd=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?wd=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('sogou')) {
      // 搜狗使用 query 参数
      if (url.includes('?')) {
        url = `${url}&query=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?query=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('zhihu')) {
      // 知乎使用 q 参数
      if (url.includes('?')) {
        url = `${url}&q=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?q=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('github')) {
      // GitHub 使用 q 参数
      if (url.includes('?')) {
        url = `${url}&q=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?q=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('bilibili')) {
      // B站使用keyword参数
      if (url.includes('?')) {
        url = `${url}&keyword=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?keyword=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('weibo')) {
      // 微博使用key参数
      if (url.includes('?')) {
        url = `${url}&key=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?key=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('yahoo')) {
      // Yahoo使用p参数
      if (url.includes('?')) {
        url = `${url}&p=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?p=${encodeURIComponent(query)}`;
      }
    } else if (lowerUrl.includes('yandex')) {
      // Yandex使用text参数
      if (url.includes('?')) {
        url = `${url}&text=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?text=${encodeURIComponent(query)}`;
      }
    } else {
      // 其他搜索引擎默认使用 q 参数
      if (url.includes('?')) {
        url = `${url}&q=${encodeURIComponent(query)}`;
      } else {
        url = `${url}?q=${encodeURIComponent(query)}`;
      }
    }
  }
  
  return url;
};

// 在浏览器中打开搜索
const openInBrowser = async (provider) => {
  if (!searchQuery.value || !provider) {
    console.log('无法执行搜索，查询或提供商为空');
    return;
  }
  
  console.log('准备打开浏览器搜索:', searchQuery.value);
  console.log('搜索提供商:', provider.name);
  
  try {
    // 将搜索词添加到历史
    addToHistory(searchQuery.value);
    
    // 构建搜索URL
    const url = buildSearchUrl(provider.url, searchQuery.value);
    console.log('搜索URL:', url);
    
    // 使用后端API打开浏览器
    console.log('调用后端API打开系统浏览器');
    await window.pywebview.api.open_browser_window(url, provider.name);
    console.log('浏览器已打开');
    
    ElMessage.success(`已在浏览器中打开"${provider.name}"搜索`);
  } catch (error) {
    console.error('打开浏览器失败:', error);
    ElMessage.error('打开浏览器失败: ' + (error.message || '未知错误'));
  }
};

// Perform search with default provider or first in the list
const performSearch = () => {
  console.log('执行搜索:', searchQuery.value);
  
  if (!searchQuery.value) {
    console.log('搜索查询为空，不执行搜索');
    return;
  }
  
  if (searchProviders.value.length === 0) {
    console.log('没有配置搜索提供商');
    ElMessage.warning('请先配置搜索提供商');
    openSearchConfig();
    return;
  }
  
  // Find default provider or use the first one
  const provider = getDefaultProvider.value;
  if (!provider) {
    console.log('未找到默认提供商');
    ElMessage.warning('未找到默认提供商');
    return;
  }
  
  console.log('使用默认提供商进行搜索:', provider.name);
  openInBrowser(provider);
};

// Add search term to history
const addToHistory = (term) => {
  if (!term.trim()) return;
  
  // 如果已存在，移除旧的记录
  const index = searchHistory.value.indexOf(term);
  if (index !== -1) {
    searchHistory.value.splice(index, 1);
  }
  
  // 添加到开头
  searchHistory.value.unshift(term);
  
  // 限制大小
  if (searchHistory.value.length > MAX_HISTORY) {
    searchHistory.value = searchHistory.value.slice(0, MAX_HISTORY);
  }
  
  // 保存到本地存储
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value));
};

// Clear search history
const clearHistory = () => {
  searchHistory.value = [];
  localStorage.removeItem('searchHistory');
};

// Show search configuration dialog
const openSearchConfig = () => {
  emit('show-config');
};

// 使用特定提供商搜索
const searchWithProvider = (provider) => {
  console.log('使用特定提供商搜索:', provider.name);
  
  if (!searchQuery.value) {
    console.log('搜索查询为空，不执行搜索');
    return;
  }
  
  openInBrowser(provider);
};

// 从历史中选择
const selectFromHistory = (term) => {
  searchQuery.value = term;
  showHistory.value = false;
  // performSearch();
};

// 修改一键多搜功能
const multiSearch = () => {
  console.log('执行一键多搜:', searchQuery.value);
  
  if (!searchQuery.value) {
    console.log('搜索查询为空，不执行搜索');
    return;
  }
  
  if (searchProviders.value.length === 0) {
    console.log('没有配置搜索提供商');
    ElMessage.warning('请先配置搜索提供商');
    openSearchConfig();
    return;
  }
  
  // 将搜索词添加到历史
  addToHistory(searchQuery.value);
  
  // 打开所有搜索提供商的搜索结果（限制最多同时打开5个，避免浏览器拦截）
  const maxOpenWindows = 5;
  const providersToUse = searchProviders.value.slice(0, maxOpenWindows);
  
  if (providersToUse.length < searchProviders.value.length) {
    ElMessage.warning(`为避免浏览器拦截，最多同时打开${maxOpenWindows}个搜索窗口`);
  }
  
  // 对每个提供商创建一个轻微延迟，避免浏览器同时打开太多标签被拦截
  providersToUse.forEach((provider, index) => {
    setTimeout(() => {
      // 构建搜索URL
      const url = buildSearchUrl(provider.url, searchQuery.value);
      console.log(`打开搜索提供商 ${provider.name}: ${url}`);
      
      // 使用后端API打开浏览器
      window.pywebview.api.open_browser_window(url, provider.name)
        .catch(error => {
          console.error(`打开搜索提供商 ${provider.name} 失败:`, error);
        });
    }, index * 300); // 每个窗口间隔300毫秒打开，避免被浏览器拦截
  });
  
  ElMessage.success(`正在打开${providersToUse.length}个搜索结果`);
};
</script>

<style lang="scss" scoped>
.search-panel-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 380px;
  height: calc(100% - 40px); /* Subtract status bar height */
  background-color: var(--el-bg-color-overlay);
  border-left: 1px solid var(--el-border-color-light);
  transition: transform 0.3s ease;
  transform: translateX(100%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: -3px 0 15px rgba(0, 0, 0, 0.1);
  
  &.visible {
    transform: translateX(0);
  }
}

.search-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: var(--el-color-primary-light-9);
  
  .title {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    font-size: 18px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .actions {
    display: flex;
    gap: 10px;
    align-items: center;
    
    .config-button {
      padding: 8px 16px;
      font-size: 15px;
      font-weight: 500;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 6px;
      height: 38px;
      background: linear-gradient(to right, var(--el-color-primary), var(--el-color-primary-light-3));
      border: none;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        background: linear-gradient(to right, var(--el-color-primary-dark-2), var(--el-color-primary));
      }
      
      .config-icon {
        font-size: 18px;
        animation: pulse 2s infinite;
        
        @keyframes pulse {
          0% { opacity: 0.8; }
          50% { opacity: 1; }
          100% { opacity: 0.8; }
        }
      }
    }
    
    .close-button {
      padding: 8px;
      height: 38px;
      width: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.fixed-content {
  padding: 16px 16px 0;
}

.scroll-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 10px;
}

.search-input-container {
  margin-bottom: 8px;
  
  .input-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .search-wrapper {
    position: relative;
    flex: 1;
    
    .search-input {
      width: 100%;
      
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }
    
    .history-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      margin-top: 2px;
      background-color: var(--el-bg-color);
      border-radius: 6px;
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
      z-index: 100;
      overflow: hidden;
      max-height: 250px;
      animation: fadeIn 0.2s ease;
      width: 80%;
      // left: 1%;
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 12px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
      
      .dropdown-list {
        max-height: 220px;
        overflow-y: auto;
        
        .history-item {
          padding: 6px 12px;
          display: flex;
          align-items: center;
          cursor: pointer;
          transition: background-color 0.2s ease;
          
          &:hover {
            background-color: var(--el-color-primary-light-9);
          }
          
          .el-icon {
            font-size: 14px;
            color: var(--el-text-color-secondary);
            margin-right: 6px;
          }
          
          .term-text {
            font-size: 13px;
            color: var(--el-text-color-primary);
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  
  .multi-search-button {
    border-radius: 6px;
    padding: 0 12px;
    height: 40px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
    white-space: nowrap;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }
  }
}

.search-providers {
  .provider-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.provider-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 14px 16px;
  transition: all 0.25s ease;
  border: 1px solid var(--el-border-color-lighter);
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: var(--el-border-color);
  }
  
  .provider-info {
    flex: 1;
    overflow: hidden;
    
    .provider-name {
      user-select: none;
      font-weight: 600;
      margin-bottom: 4px;
      color: var(--el-text-color-primary);
    }
    
    .provider-url {
      color: var(--el-text-color-secondary);
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  
  .provider-actions {
    margin-left: 12px;
    
    .open-button {
      border-radius: 6px;
      padding: 6px 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.2s;
      
      &:hover:not(:disabled) {
        transform: translateY(-1px);
      }
    }
  }
}

// 添加禁止选择文本的样式类
.no-select {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}
</style> 