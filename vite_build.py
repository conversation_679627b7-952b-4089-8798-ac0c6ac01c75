from bottle import Bottle, run, static_file
import threading

from backend.dev import run_build
from backend.bridge.API import API
from backend.pvvruntime.Runtime import WebViewApp
port=None
# 处理自带服务有问题
app = Bottle()
# 静态文件目录
@app.route('/assets/<filepath:path>')
def serve_static(filepath):
    return static_file(filepath, root='./statics/assets')

@app.route('/')
def index():
    with open('./statics/index.html', 'r') as f:
        html_content = f.read()
    return html_content


def run_server():
    """运行 Bottle 服务器"""

    port = 32453
    run(app, host='127.0.0.1', port=port)

if __name__ == "__main__":
    # 启动 Bottle 服务器
    run_build()
    # threading.Thread(target=run_server, daemon=True).start()
    # gui = WebViewApp(900 ,800,draggable=True)
    # gui.run(f'http://127.0.0.1:{32453}',js_api=API())

