# 原来pywebview==5.3.2存在js注入时机的问题，导致vue开发会出现问题，修复到start注入
import platform

import webview
from backend.bridge.API import API
import os
import hashlib
import json
from pathlib import Path
import sys
import threading
import time
import logging
import hmac



# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.expanduser('~'), '.pvv', 'integrity.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('PVV-Integrity')

# 硬编码的签名密钥 - 在实际应用中应更加隐蔽并使用更复杂的保护机制
SIGNATURE_KEY = b"PVV_8f2a61a4e9b94a31b8ea8d6e2f13f308_SECRET_KEY"

# 硬编码的关键文件哈希值 - 此处仅作为示例，实际应由构建脚本自动填充
# 这些是最关键的不能被修改的文件
CRITICAL_FILE_HASHES = {}

class IntegrityChecker:
    """增强型前端文件完整性检查器"""

    def __init__(self, statics_dir, check_interval=30):
        """
        初始化完整性检查器
        
        :param statics_dir: 静态文件目录
        :param check_interval: 检查间隔(秒)，默认5分钟
        """
        self.statics_dir = statics_dir
        self.manifest_path = os.path.join(statics_dir, ".integrity")  # 隐藏文件
        self.check_interval = check_interval
        self.stop_event = threading.Event()
        self.is_running = False
        self.manifest = self._load_manifest()

    def _load_manifest(self):
        """加载完整性清单文件（带签名验证）"""
        try:
            if not os.path.exists(self.manifest_path):
                logger.warning("完整性清单文件不存在，将在开发模式下自动生成")
                return None

            with open(self.manifest_path, 'rb') as f:
                raw_data = f.read()

            # 分离签名和数据 - 修正签名长度为32字节而非64字节
            signature = raw_data[:32]  # HMAC-SHA256签名长度为32字节
            data = raw_data[32:]       # 剩余部分为数据

            # 验证签名
            expected_signature = hmac.new(SIGNATURE_KEY, data, hashlib.sha256).digest()
            if not hmac.compare_digest(signature, expected_signature):
                logger.critical("完整性清单签名验证失败！可能被篡改")
                return None

            # 解码JSON
            return json.loads(data.decode('utf-8'))

        except Exception as e:
            logger.error(f"加载完整性清单失败: {str(e)}")
            return None

    def calculate_file_hash(self, file_path):
        """计算文件的SHA-256哈希值"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {str(e)}")
            return None

    def generate_integrity_manifest(self):
        """生成带签名的文件完整性清单"""
        manifest = {}
        file_count = 0

        # 扫描所有文件
        for path in Path(self.statics_dir).rglob('*'):
            if path.is_file() and path.name != ".integrity":
                relative_path = str(path.relative_to(self.statics_dir))
                file_hash = self.calculate_file_hash(path)
                if file_hash:
                    manifest[relative_path] = file_hash
                    file_count += 1

        # 序列化数据
        json_data = json.dumps(manifest, sort_keys=True).encode('utf-8')

        # 生成签名
        signature = hmac.new(SIGNATURE_KEY, json_data, hashlib.sha256).digest()

        # 保存签名+数据
        try:
            with open(self.manifest_path, 'wb') as f:
                f.write(signature + json_data)

            logger.info(f"已生成签名保护的完整性清单，包含 {file_count} 个文件")
            return manifest
        except Exception as e:
            logger.error(f"保存完整性清单失败: {str(e)}")
            return None

    def verify_integrity(self):
        """验证文件完整性，包括硬编码哈希和清单哈希"""
        try:
            modified_files = []

            # 第1步：验证关键文件 (通过硬编码哈希)
            for file_path, expected_hash in CRITICAL_FILE_HASHES.items():
                full_path = os.path.join(self.statics_dir, file_path)

                if not os.path.exists(full_path):
                    logger.critical(f"关键文件丢失: {file_path}")
                    modified_files.append(f"关键丢失: {file_path}")
                    continue

                actual_hash = self.calculate_file_hash(full_path)
                if actual_hash != expected_hash:
                    logger.critical(f"关键文件被修改: {file_path}")
                    modified_files.append(f"关键修改: {file_path}")

            # 第2步：验证签名和加载清单
            if not self.manifest:
                # 尝试重新加载清单
                self.manifest = self._load_manifest()
                if not self.manifest:
                    logger.critical("无法加载或验证完整性清单！")
                    modified_files.append("完整性清单验证失败")
                    return False, modified_files

            # 第3步：用清单验证其他文件
            for file_path, expected_hash in self.manifest.items():
                # 跳过已在关键文件中检查过的
                if file_path in CRITICAL_FILE_HASHES:
                    continue

                full_path = os.path.join(self.statics_dir, file_path)

                if not os.path.exists(full_path):
                    logger.warning(f"文件丢失: {file_path}")
                    modified_files.append(f"丢失: {file_path}")
                    continue

                actual_hash = self.calculate_file_hash(full_path)
                if actual_hash != expected_hash:
                    logger.warning(f"文件被修改: {file_path}")
                    modified_files.append(f"修改: {file_path}")

            # 第4步：检查是否有新增文件
            for path in Path(self.statics_dir).rglob('*'):
                if path.is_file() and path.name != ".integrity":
                    relative_path = str(path.relative_to(self.statics_dir))
                    if relative_path not in self.manifest and relative_path not in CRITICAL_FILE_HASHES:
                        logger.warning(f"发现未知文件: {relative_path}")
                        modified_files.append(f"未知: {relative_path}")

            return len(modified_files) == 0, modified_files
        except Exception as e:
            logger.error(f"完整性验证出现异常: {str(e)}")
            if os.environ.get("PVV_FORCE_START") == "1":
                # 紧急情况：允许强制启动
                return True, []
            return False, ["验证过程出错"]

    def start_periodic_check(self, window=None):
        """启动定期检查线程，并添加随机性防止逃避检测"""
        if self.is_running:
            return

        self.is_running = True
        self.stop_event.clear()

        def check_thread():
            logger.info(f"完整性检查线程已启动")

            # 防逃避变量
            next_check = 0

            while not self.stop_event.is_set():
                # 随机变化检查间隔，防止预测
                if time.time() >= next_check:
                    # 执行完整性检查
                    is_intact, modified_files = self.verify_integrity()

                    if not is_intact:
                        logger.critical(f"检测到文件修改或签名失败: {', '.join(modified_files[:5])}")

                        if window:
                            try:
                                # 发送警告
                                window.evaluate_js("""
                                    alert('文件损坏，程序将退出！');
                                """)
                                # 给用户时间看警告
                                time.sleep(2)
                                # 退出程序
                                window.destroy()
                            except:
                                # 如果JS执行失败，直接退出
                                os._exit(1)
                        else:
                            # 如果没有窗口对象，直接退出
                            os._exit(1)

                    # 设置下次检查时间（有随机变化）
                    jitter = self.check_interval * 0.2  # 20%的随机变化
                    random_interval = self.check_interval + (hash(time.time()) % int(jitter)) - (jitter / 2)
                    next_check = time.time() + random_interval

                # 更短的等待间隔，提高响应性
                time.sleep(1)

        # 启动检查线程
        check_thread = threading.Thread(target=check_thread, daemon=True)
        check_thread.start()

        return check_thread

    def stop(self):
        """停止检查线程"""
        self.stop_event.set()
        self.is_running = False

if __name__ == '__main__':

    # 获取当前的路径
    base_dir = os.path.abspath(os.curdir)
    statics_dir = os.path.join(base_dir, "statics")


    dev_mode = False

    # 添加更清晰的注释说明使用场景
    if dev_mode:
        logger.info("开发模式：生成完整性清单而不是验证")
        logger.info("注意: 仅在开发或重新生成清单时使用此模式")
        integrity_checker = IntegrityChecker(statics_dir)
        integrity_checker.generate_integrity_manifest()
    else:
        # 初始化完整性检查器
        integrity_checker = IntegrityChecker(statics_dir)

        # 启动时验证一次
        is_intact, modified_files = integrity_checker.verify_integrity()
        if not is_intact:
            logger.critical(f"启动检查：检测到文件被篡改: {', '.join(modified_files[:5])}")
            print("程序文件完整性验证失败，程序无法启动")
            sys.exit(1)

    # 创建窗口
    window = webview.create_window(
        title="PVV",
        width=1080,
        height=800,
        url='./statics/index.html',
        js_api=API(),
        frameless=True,
        resizable=True
    )

    # 启动完整性检查线程
    if not dev_mode:
        integrity_checker.start_periodic_check(window)



    # 启动应用
    webview.start(debug=False, gui="edgechromium",icon='./logo.ico')
