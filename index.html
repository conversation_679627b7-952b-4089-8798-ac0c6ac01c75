<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI写作助手 - 原型设计</title>
    <style>
        :root {
            --primary-color: #409eff;
            --success-color: #67c23a;
            --warning-color: #e6a23c;
            --danger-color: #f56c6c;
            --info-color: #909399;
            --text-color: #303133;
            --text-color-secondary: #606266;
            --border-color: #dcdfe6;
            --bg-color: #f5f7fa;
            --header-height: 60px;
            --sidebar-width: 240px;
            --assistant-width: 400px;
            --shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            --transition-duration: 0.3s;
        }



        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        /* 顶部工具栏 */
        .app-header {
            height: var(--header-height);
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 20px;
            justify-content: space-between;
            box-shadow: var(--shadow);
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 侧边栏 */
        .app-sidebar {
            width: var(--sidebar-width);
            background-color: #fff;
            border-right: 1px solid var(--border-color);
            height: 100%;
            overflow-y: auto;
            flex-shrink: 0;
            transition: width var(--transition-duration);
        }

        /* 主编辑区域 */
        .app-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #fff;
            transition: margin-right var(--transition-duration);
            position: relative;
        }

        .app-main.with-assistant {
            margin-right: var(--assistant-width);
        }

        .editor-toolbar {
            height: 40px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 10px;
            background-color: #fafafa;
        }

        .editor-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            position: relative;
        }

        .editor-content-inner {
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.8;
            min-height: 100%;
        }

        .chapter-title {
            font-size: 24px;
            margin-bottom: 30px;
            font-weight: bold;
            text-align: center;
        }

        .editor-status {
            height: 30px;
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 15px;
            background-color: #fafafa;
            font-size: 12px;
            color: var(--text-color-secondary);
            justify-content: space-between;
        }

        /* AI助手面板 */
        .ai-assistant {
            width: var(--assistant-width);
            height: 100%;
            border-left: 1px solid var(--border-color);
            position: absolute;
            right: 0;
            top: 0;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform var(--transition-duration);
            z-index: 90;
        }

        .ai-assistant.active {
            transform: translateX(0);
        }

        .assistant-header {
            height: 50px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 15px;
            justify-content: space-between;
            background-color: #f0f9ff;
        }

        .assistant-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .assistant-title .icon {
            font-size: 18px;
        }

        .assistant-actions {
            display: flex;
            gap: 10px;
        }

        .assistant-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .assistant-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .message-row {
            display: flex;
        }

        .message.user .message-row {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: bold;
            margin: 0 10px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background-color: var(--primary-color);
        }

        .message.assistant .message-avatar {
            background-color: var(--success-color);
        }

        .message-content {
            max-width: 80%;
            border-radius: 8px;
            padding: 10px 12px;
            position: relative;
            word-break: break-word;
        }

        .message.user .message-content {
            background-color: #ecf5ff;
            border: 1px solid #d9ecff;
            border-top-right-radius: 0;
        }

        .message.assistant .message-content {
            background-color: #f0f9eb;
            border: 1px solid #e1f3d8;
            border-top-left-radius: 0;
        }

        .entity-reference {
            display: inline-block;
            background-color: rgba(64, 158, 255, 0.1);
            border-radius: 4px;
            padding: 0px 5px;
            margin: 0 2px;
            color: var(--primary-color);
            border: 1px solid rgba(64, 158, 255, 0.2);
            cursor: pointer;
        }

        .message-time {
            font-size: 12px;
            color: var(--text-color-secondary);
            margin-top: 5px;
            text-align: center;
        }

        .message-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 5px;
            gap: 5px;
        }

        .message-action-btn {
            background: none;
            border: none;
            font-size: 12px;
            color: var(--text-color-secondary);
            cursor: pointer;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .message-action-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--primary-color);
        }

        .assistant-input {
            border-top: 1px solid var(--border-color);
            padding: 10px 15px;
            background-color: #fafafa;
        }

        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }

        .quick-action-btn {
            background-color: #f4f4f5;
            border: 1px solid #e4e7ed;
            color: var(--text-color-secondary);
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quick-action-btn:hover {
            background-color: #ecf5ff;
            border-color: #c6e2ff;
            color: var(--primary-color);
        }

        .entity-menu {
            margin-bottom: 10px;
        }

        .entity-menu-btn {
            background-color: #f4f4f5;
            border: 1px solid #e4e7ed;
            color: var(--text-color-secondary);
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .entity-menu-btn:hover {
            background-color: #ecf5ff;
            border-color: #c6e2ff;
            color: var(--primary-color);
        }

        .entity-dropdown {
            position: relative;
            display: inline-block;
        }

        .entity-list {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            min-width: 150px;
            background-color: #fff;
            box-shadow: var(--shadow);
            border-radius: 4px;
            padding: 5px 0;
            margin-bottom: 5px;
            z-index: 100;
            max-height: 300px;
            overflow-y: auto;
        }

        .entity-dropdown.active .entity-list {
            display: block;
        }

        .entity-category {
            padding: 8px 12px;
            font-size: 12px;
            color: var(--text-color-secondary);
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
            background-color: #f5f7fa;
        }

        .entity-item {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .entity-item:hover {
            background-color: #f5f7fa;
        }

        .input-container {
            position: relative;
        }

        .input-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            resize: none;
            font-size: 14px;
            line-height: 1.5;
            transition: all 0.2s;
            box-shadow: 0 0 0 0 transparent;
        }

        .input-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .input-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }

        .btn {
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: #fff;
        }

        .btn-primary:hover {
            background-color: #66b1ff;
        }

        .btn-text {
            background: none;
            color: var(--text-color-secondary);
        }

        .btn-text:hover {
            color: var(--primary-color);
            background-color: rgba(64, 158, 255, 0.1);
        }

        /* 样式增强 */
        .ai-assistant-toggle {
            position: absolute;
            top: 50%;
            left: -16px;
            transform: translateY(-50%);
            width: 16px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 4px 0 0 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
        }

        /* 工具按钮 */
        .tool-btn {
            padding: 6px 12px;
            background-color: #fff;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .tool-btn:hover {
            color: var(--primary-color);
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }

        .tool-btn.active {
            color: var(--primary-color);
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }

        /* 调整大小控制器 */
        .resize-handle {
            position: absolute;
            left: -5px;
            top: 0;
            width: 10px;
            height: 100%;
            cursor: ew-resize;
            z-index: 100;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            :root {
                --assistant-width: 320px;
            }
            
            .app-sidebar {
                width: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 应用容器 -->
    <header class="app-header">
        <div class="header-left">
            <button class="tool-btn">
                <i class="icon">←</i>
                <span>返回</span>
            </button>
            <span style="margin-left: 20px; font-weight: bold;">小说编辑器</span>
        </div>
        <div class="header-right">
            <button class="tool-btn" id="aiToggleBtn">
                <i class="icon">💬</i>
                <span>AI助手</span>
            </button>
            <button class="tool-btn">
                <i class="icon">💾</i>
                <span>保存</span>
            </button>
        </div>
    </header>

    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="app-sidebar">
            <!-- 章节列表 - 简化显示 -->
            <div style="padding: 15px;">
                <div style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 10px;">
                    <div style="font-weight: bold; margin-bottom: 5px;">第一卷 荒原之始</div>
                    <div style="margin-left: 15px; color: #606266; padding: 5px 0;">序章 启程</div>
                    <div style="margin-left: 15px; color: #606266; padding: 5px 0; background: #ecf5ff;">第一章 编辑器</div>
                    <div style="margin-left: 15px; color: #606266; padding: 5px 0;">第二章 离别</div>
                </div>
                <div style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 10px;">
                    <div style="font-weight: bold; margin-bottom: 5px;">第二卷 城市传说</div>
                    <div style="margin-left: 15px; color: #606266; padding: 5px 0;">第三章 旅途</div>
                    <div style="margin-left: 15px; color: #606266; padding: 5px 0;">第四章 邂逅</div>
                </div>
            </div>
        </div>

        <!-- 主编辑区 -->
        <div class="app-main" id="appMain">
            <div class="editor-toolbar">
                <!-- 编辑器工具栏 - 简化显示 -->
                <div style="display: flex; gap: 10px;">
                    <button class="tool-btn">
                        <i class="icon">🔤</i>
                        <span>字体</span>
                    </button>
                    <button class="tool-btn">
                        <i class="icon">🖼️</i>
                        <span>背景</span>
                    </button>
                    <button class="tool-btn">
                        <i class="icon">📊</i>
                        <span>统计</span>
                    </button>
                </div>
            </div>

            <div class="editor-content">
                <div class="editor-content-inner">
                    <div class="chapter-title">第一章 编辑器</div>
                    <div style="text-indent: 2em; margin-bottom: 1em;">
                        许应示意道寂真君停下脚步，道："道友，看好圣祖。"说罢，向道皇走去。
                    </div>
                    <div style="text-indent: 2em; margin-bottom: 1em;">
                        圣祖道："道寂，你亦是修行寂灭之人，我乃寂灭大道的元始，可以指点你如何修行，让你成就元始！我辈修行寂灭，猖狂于宇宙之间，筑营在群雄之列，何须奇人窥下？"
                    </div>
                    <div style="text-indent: 2em; margin-bottom: 1em;">
                        道寂真君问道："修成寂灭元始，能打赢面前这三人中的哪个？"
                    </div>
                    <div style="text-indent: 2em; margin-bottom: 1em;">
                        圣祖皱眉。道寂口中的面前三人，正是道皇、许应道友、太安道人，乃是当今天地间道行最精深的三位大能。
                    </div>
                    <div style="text-indent: 2em; margin-bottom: 1em;">
                        "难道这就是你的追求？比较谁胜谁负？"圣祖不屑地道："修行的意义乃是探索天地大道的奥秘，求得长生不死，追寻生命的终极意义！与人争高下，不过是无聊之徒的把戏罢了。"
                    </div>
                </div>
            </div>

            <div class="editor-status">
                <div>总字数: 4,297 | 选中字数: 0 | 当前段落: 76字</div>
                <div>当前时间: 16:38:28</div>
            </div>
        </div>

        <!-- AI写作助手面板 -->
        <div class="ai-assistant" id="aiAssistant">
            <div class="ai-assistant-toggle" id="aiToggle">
                <i style="font-size: 12px;">◀</i>
            </div>
            <div class="resize-handle" id="resizeHandle"></div>
            <div class="assistant-header">
                <div class="assistant-title">
                    <i class="icon">💬</i>
                    AI写作助手
                </div>
                <div class="assistant-actions">
                    <button class="tool-btn active" title="记忆模式">
                        <i class="icon">🧠</i>
                    </button>
                    <button class="tool-btn" title="清空对话">
                        <i class="icon">🗑️</i>
                    </button>
                </div>
            </div>

            <div class="assistant-body">
                <div class="assistant-messages" id="messages">
                    <!-- 系统消息 -->
                    <div class="message system">
                        <div class="message-time">今天 16:20</div>
                    </div>

                    <!-- AI助手消息 -->
                    <div class="message assistant">
                        <div class="message-row">
                            <div class="message-avatar">AI</div>
                            <div class="message-content">
                                您好！我是您的AI写作助手，可以帮助您完成小说创作。我可以：
                                <ul style="margin-left: 20px; margin-top: 5px;">
                                    <li>提供情节建议和灵感</li>
                                    <li>帮助塑造人物角色</li>
                                    <li>优化文字表达</li>
                                    <li>解决创作中的问题</li>
                                </ul>
                                您可以直接提问，或使用快捷指令开始。当前正在编辑的章节是：<b>第一章 编辑器</b>
                            </div>
                        </div>
                        <div class="message-actions">
                            <button class="message-action-btn">复制</button>
                        </div>
                    </div>

                    <!-- 用户消息 -->
                    <div class="message user">
                        <div class="message-row">
                            <div class="message-avatar">我</div>
                            <div class="message-content">
                                帮我构思一下接下来圣祖和道寂之间的对话，表现出圣祖的傲慢与自大，同时也要展现出道寂的智慧与沉稳。
                            </div>
                        </div>
                    </div>

                    <!-- AI助手回复 -->
                    <div class="message assistant">
                        <div class="message-row">
                            <div class="message-avatar">AI</div>
                            <div class="message-content">
                                <p>圣祖轻蔑一笑，眼中闪过一丝不屑："你这小辈，何以知晓大道真谛？我存在之时，你还未曾出世。寂灭之道，讲究的是超脱，是不拘于凡俗的执念。你却在这里谈论胜负，着实可笑。"</p>
                                <p>道寂真君不急不躁，嘴角微扬："<span class="entity-reference">圣祖</span>大人说得极是。大道无形，执着胜负确实可笑。可我只是好奇，既为大道元始，为何落得被囚之境？若真超脱，又岂会为一时之困所扰？"</p>
                                <p>圣祖面色一沉："狂妄！你以为短短数千年修行，就能窥探我等存在亿万年的智慧？寂灭大道自有轮回，今日受困，不过是为来日大兴做准备罢了。"</p>
                                <p>道寂不慌不忙，轻抚胡须："受教了。只是不知<span class="entity-reference">圣祖</span>大人，既然一切尽在掌握，为何方才眼中会闪过一丝惊慌？大道无情，却也最能照见本心啊。"</p>
                            </div>
                        </div>
                        <div class="message-actions">
                            <button class="message-action-btn">复制</button>
                            <button class="message-action-btn">插入编辑器</button>
                        </div>
                    </div>
                </div>

                <div class="assistant-input">
                    <div class="quick-actions">
                        <button class="quick-action-btn">角色设计</button>
                        <button class="quick-action-btn">情节构思</button>
                        <button class="quick-action-btn">描写优化</button>
                        <button class="quick-action-btn">文风调整</button>
                        <button class="quick-action-btn">续写当前段落</button>
                    </div>

                    <div class="entity-menu">
                        <div class="entity-dropdown" id="entityDropdown">
                            <button class="entity-menu-btn" id="entityMenuBtn">
                                <i class="icon">@</i>
                                <span>引用实体</span>
                            </button>
                            <div class="entity-list">
                                <div class="entity-category">主要角色</div>
                                <div class="entity-item">圣祖</div>
                                <div class="entity-item">道寂真君</div>
                                <div class="entity-item">许应</div>
                                <div class="entity-item">太安道人</div>
                                <div class="entity-category">地点</div>
                                <div class="entity-item">道皇城</div>
                                <div class="entity-item">寂灭秘境</div>
                                <div class="entity-category">物品</div>
                                <div class="entity-item">寂灭之剑</div>
                                <div class="entity-item">道皇印</div>
                            </div>
                        </div>
                    </div>

                    <div class="input-container">
                        <textarea class="input-textarea" rows="4" placeholder="输入消息或@引用实体..."></textarea>
                    </div>

                    <div class="input-actions">
                        <button class="btn btn-text">
                            <i class="icon">🔄</i>
                            添加选中文本
                        </button>
                        <button class="btn btn-primary">
                            <i class="icon">📤</i>
                            发送
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // AI助手面板切换
            const aiAssistant = document.getElementById('aiAssistant');
            const appMain = document.getElementById('appMain');
            const aiToggleBtn = document.getElementById('aiToggleBtn');
            const aiToggle = document.getElementById('aiToggle');
            
            // 初始显示AI助手
            aiAssistant.classList.add('active');
            appMain.classList.add('with-assistant');
            
            function toggleAssistant() {
                aiAssistant.classList.toggle('active');
                appMain.classList.toggle('with-assistant');
            }
            
            aiToggleBtn.addEventListener('click', toggleAssistant);
            aiToggle.addEventListener('click', toggleAssistant);
            
            // 实体引用菜单
            const entityMenuBtn = document.getElementById('entityMenuBtn');
            const entityDropdown = document.getElementById('entityDropdown');
            
            entityMenuBtn.addEventListener('click', function() {
                entityDropdown.classList.toggle('active');
            });
            
            // 点击其他地方关闭实体菜单
            document.addEventListener('click', function(e) {
                if (!entityDropdown.contains(e.target)) {
                    entityDropdown.classList.remove('active');
                }
            });
            
            // 实体项点击事件
            const entityItems = document.querySelectorAll('.entity-item');
            entityItems.forEach(item => {
                item.addEventListener('click', function() {
                    const textarea = document.querySelector('.input-textarea');
                    const entityName = this.textContent;
                    textarea.value += `@${entityName} `;
                    textarea.focus();
                    entityDropdown.classList.remove('active');
                });
            });
            
            // 快捷指令点击事件
            const quickActionBtns = document.querySelectorAll('.quick-action-btn');
            quickActionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const textarea = document.querySelector('.input-textarea');
                    const actionText = this.textContent;
                    textarea.value = `[${actionText}] `;
                    textarea.focus();
                });
            });
            
            // AI消息中的操作按钮
            const actionBtns = document.querySelectorAll('.message-action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.textContent === '插入编辑器') {
                        alert('内容已插入编辑器！');
                    } else if (this.textContent === '复制') {
                        alert('内容已复制到剪贴板！');
                    }
                });
            });
            
            // 调整大小功能
            const resizeHandle = document.getElementById('resizeHandle');
            let isResizing = false;
            
            resizeHandle.addEventListener('mousedown', function(e) {
                isResizing = true;
                document.addEventListener('mousemove', handleResize);
                document.addEventListener('mouseup', stopResize);
                e.preventDefault();
            });
            
            function handleResize(e) {
                if (!isResizing) return;
                
                // 计算新宽度 (从窗口右侧到鼠标位置)
                const newWidth = window.innerWidth - e.clientX;
                
                // 设置最小宽度限制
                const minWidth = 280;
                const maxWidth = window.innerWidth * 0.5;
                
                if (newWidth >= minWidth && newWidth <= maxWidth) {
                    document.documentElement.style.setProperty('--assistant-width', newWidth + 'px');
                }
            }
            
            function stopResize() {
                isResizing = false;
                document.removeEventListener('mousemove', handleResize);
                document.removeEventListener('mouseup', stopResize);
            }
        });
    </script>
</body>
</html> 