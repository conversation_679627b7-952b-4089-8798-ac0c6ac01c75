irm https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_win_id_modifier.ps1 | iex


https://developer.microsoft.com/en-us/microsoft-edge/webview2/?form=MA13LH

nuitka .\main.py --standalone   --windows-disable-console --show-memory  --show-progress   --output-dir=out --include-data-dir="D:\project\go\pvv\statics=statics" --include-data-file="D:\project\go\pvv\default.docx=docx/templates/default.docx"      --include-data-dir="D:\project\go\pvv\webview\js=webview/js"

nuitka .\main.py --standalone --windows-disable-console --show-memory --show-progress --output-dir=out --include-data-dir="D:\project\go\pvv\statics=statics" --include-data-file="D:\project\go\pvv\default.docx=docx/templates/default.docx" --include-data-dir="D:\project\go\pvv\webview\js=webview/js" --jobs=5

npm install --save-dev rollup-plugin-obfuscator javascript-obfuscator

--windows-disable-console

# 混淆教程 https://blog.csdn.net/Gas_station/article/details/134602542

1、第一步创建一个vue3项目修改vite文件，这样build_test.py可以做到build到指定目录，这个目录是为了nuitka打包使用
2、配置main.py相关代码，启动
3、dev是vite vue3开发的时候测试使用，这个主要能绑定一些python方法到js上，方便开发测试
4、打包发布。


lock-scroll  能用来el弹窗 锁定不滚动。


todo:
# https://pywebview.flowrl.com/guide/api.html#webview-settings  支持file协议，这样可以优化我的背景图相关的逻辑，这样就不用处理这个文件的base64加载背景图变得十分快速
https://pywebview.flowrl.com/examples/settings.html
webview.settings = {
  'ALLOW_DOWNLOADS': False,
  'ALLOW_FILE_URLS': True,
  'OPEN_EXTERNAL_LINKS_IN_BROWSER': True,
  'OPEN_DEVTOOLS_IN_DEBUG': True
}

修改上面的编辑文本，自动保存调到最后了。

书籍的功能方法没有了
码字状态信息不能用。




阅读的时候跟着标记每一行。

mac bug：
拖动bug
项目启动bug

file映射算是windows特有的优化，mac可能不支持，现在使用这个版本只是暂时的。


todo:
ai助手弹窗支持扩展大模型修改编辑区域的能力，类似于把编辑的能力交给大模型，按照一定的方法对应起来。类似mcp




最后添加一个上次记录处理的书籍的id的能力，场景卡那些。

chat代码区域 主题的能力

多个通化后台的不能同时处理。

背景应该是底层，上层适配底层，添加样式就能透明了。

还有消息对话，用户的消息只有在完成对话后才能加入后台保存。





后端的打开文件，打开目录等调用抽象一下，不用重复定义，前端添加通用的方案支持自动生成供调用js方法。


现在自动文件夹备份的能力不能自动删除超过的数量的备份了。这个可以修复。
删除备份失败 E:\pvv备份\backup_20250322_141704: [WinError 5] 拒绝访问。: 'E:\\pvv备份\\backup_20250322_141704\\.git\\objects\\00\\0664cfa4e5676fec04d67c32c670617d16c775'




技巧：
先整体设置主题
修复样式的时候通过class指定



机器码的实现应该整体加密解密得到的结果不能是base64,人家可以直接修改模拟时间了。



时间线变成三条竖线的逻辑。



AB界面存在bug，ui编辑配置的时候删除的保存后还在。这时候可以使用导入导出。需要修复

pvv.py中CRITICAL_FILE_HASHES只需要注释就能安装一个免检测的statics文件夹，这样只用来修复前端就行


ab界面配置保存有问题，现在的设计和配置的保存优干扰，暂时不修复。












关于两个dll找不到的问题，下面是测试代码，主要的逻辑就是两个自动安装的库版本需要对应：
PyQt6-WebEngine           6.3.0
PyQt6-WebEngine-Qt6       6.3.0

PyQt6                     6.3.1
PyQt6-Qt6                 6.3.2

完整的安装的pyqt6相关的库：
PyQt6                     6.3.1
pyqt6-plugins             *******.3
PyQt6-Qt                  6.0.1
PyQt6-Qt6                 6.3.2
PyQt6_sip                 13.10.2
pyqt6-tools               *******.3
PyQt6-WebEngine           6.3.0
PyQt6-WebEngine-Qt6       6.3.0



# 第一步：测试最核心的DLL
from PyQt6 import QtCore
print(QtCore.QT_VERSION_STR)


# 第二步：测试 WebEngine 核心
from PyQt6 import QtWebEngineCore
print(QtWebEngineCore)








webview2全编译：
nuitka .\PVV.py --standalone  --nofollow-import-to=pyqt6 --windows-console-mode=disable  --show-memory  --show-progress --output-dir=winout --include-data-dir="D:\project\go\pvv\statics=statics" --include-data-file="D:\project\go\pvv\default.docx=docx/templates/default.docx" --include-data-dir="D:\project\go\pvv\webview\js=webview/js"  --windows-icon-from-ico="D:\project\go\pvv\logo.ico"





qt可用编译命令
  nuitka .\PVV.py  --windows-console-mode=disable   --enable-plugins=pyqt5   --include-package=qtpy   --include-module=PyQt5.QtWebEngineWidgets,webview.platforms.qt    --standalone  --show-memory --show-progress --output-dir=out --include-data-dir="D:\project\go\pvv\statics=statics" --include-data-dir="D:\project\go\pvv\venv\Lib\site-packages\PyQt5\Qt5\translations=PyQt5/Qt5/translations"   --include-data-dir="D:\project\go\pvv\venv\Lib\site-packages\PyQt5\Qt5\resources=PyQt5/Qt5/resources"    --include-data-file="D:\project\go\pvv\default.docx=docx/templates/default.docx" --include-data-dir="D:\project\go\pvv\webview\js=webview/js"   --windows-icon-from-ico="D:\project\go\pvv\logo.ico"  --disable-plugin=pywebview

  这个命令就是没有打包qtpy库，然后复制pyqt5的两个资源文件,还有 webview需要复制qt通过plugin来修复替换我们自己的代码。






更完善的思维导图：https://wanglin2.github.io/mind-map/#/