import os
import sys
import subprocess
import re
import shutil
import time
import glob
import pkgutil
import importlib
from pathlib import Path
import platform
import argparse  # 新增：导入参数解析模块

# 配置参数
MAX_ATTEMPTS = 150  # 最大尝试次数
TIMEOUT = 5  # 程序运行的超时时间（秒）
# 这两个参数需要根据实际项目修改
COMPILED_EXE_PATH = "./out/main.dist/main.exe"  # 编译后的可执行文件路径
DESTINATION_PATH = "./out/main.dist"  # 编译结果目录

# 运行模式 - 将由命令行参数决定
AUTO_MODE = True  # 默认为全自动模式

# 检测是否是64位Python
IS_64BIT = platform.architecture()[0] == '64bit'

# 特殊模块与其对应的DLL依赖映射 - 可以按需扩展
SPECIAL_MODULE_DLL_MAP = {
    '_sqlite3': ['sqlite3.dll', 'sqlite3.*.dll'],
    '_ssl': ['libssl-*.dll', 'libcrypto-*.dll', 'ssleay32.dll', 'libeay32.dll'],
    '_hashlib': ['libcrypto-*.dll'],
    '_socket': ['select.pyd'],
    '_overlapped': [],
    '_asyncio': ['_overlapped.pyd'],
    '_decimal': ['libmpdec.dll'],
    'unicodedata': [],
    'pyexpat': ['libexpat.dll'],
    'win32api': ['pywintypes*.dll', 'pythoncom*.dll'],
    'win32com': ['pywintypes*.dll', 'pythoncom*.dll'],
    'ctypes': ['libffi*.dll'],
    '_ctypes': ['libffi*.dll'],
    '_lzma': ['liblzma.dll'],
    '_bz2': ['libbz2.dll'],
    'PIL._imaging': ['zlib.dll'],
}

# 需要查找的扩展名（用于完整包复制）
MODULE_EXTENSIONS = ['.py', '.pyc', '.pyd', '.so', '.dll']

# 已知可能需要完整复制的模块（可以根据项目需要添加）
# 保留但缩减列表，只作为预先复制的候选项
COMMON_FULL_PACKAGES = [
    'docx', 'lxml', 'pandas', 'numpy', 'openpyxl',
    'PIL', 'matplotlib', 'requests'
]

# 特殊库的连带依赖关系
PACKAGE_DEPENDENCIES = {
    'docx': ['lxml', 'docx.templates', 'docx.parts', 'docx.opc', 'docx.oxml'],
    'html2docx': ['docx', 'bs4'],
    'pandas': ['numpy', 'pytz', 'dateutil'],
    'matplotlib': ['numpy', 'cycler', 'pyparsing', 'kiwisolver', 'PIL'],
    'openai': ['tiktoken', 'requests', 'aiohttp']
}

# 添加一个全局变量用于记录所有复制操作
COPY_OPERATIONS = []

def record_copy_operation(source, destination, operation_type="FILE"):
    """记录复制操作"""
    global COPY_OPERATIONS
    COPY_OPERATIONS.append({
        'source': source,
        'destination': destination,
        'type': operation_type,  # 可以是 FILE, PACKAGE, DLL 等
        'timestamp': time.time()
    })
    
def copy_file(source_file, dest_file):
    """复制单个文件并记录操作"""
    try:
        os.makedirs(os.path.dirname(dest_file), exist_ok=True)
        shutil.copy2(source_file, dest_file)
        record_copy_operation(source_file, dest_file, "FILE")
        print(f"已复制文件: {source_file} -> {dest_file}")
        return True
    except Exception as e:
        print(f"复制文件失败: {e}")
        return False

def get_system_info():
    """获取系统信息"""
    info = {
        'python_version': sys.version,
        'python_path': sys.executable,
        'platform': platform.platform(),
        'architecture': platform.architecture()[0],
        'is_venv': hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix),
        'sys_prefix': sys.prefix,
    }
    
    if hasattr(sys, 'real_prefix'):
        info['real_prefix'] = sys.real_prefix
    elif hasattr(sys, 'base_prefix'):
        info['base_prefix'] = sys.base_prefix
        
    return info

def get_python_paths():
    """获取Python库路径、site-packages路径和DLLs路径"""
    info = get_system_info()
    lib_paths = []
    site_packages_paths = []
    dll_paths = []
    
    # 1. 从sys.path获取路径
    for p in sys.path:
        if p and os.path.exists(p):
            if 'site-packages' in p:
                site_packages_paths.append(p)
            elif os.path.exists(os.path.join(p, 'os.py')):
                lib_paths.append(p)
                
    # 2. 从sys.prefix获取标准路径
    if info['is_venv']:
        # 虚拟环境
        prefix = info.get('real_prefix', info.get('base_prefix', sys.prefix))
        sys_prefix = sys.prefix
        
        # 虚拟环境的标准库路径
        if os.name == 'nt':  # Windows
            lib_paths.append(os.path.join(sys_prefix, 'Lib'))
            site_packages_paths.append(os.path.join(sys_prefix, 'Lib', 'site-packages'))
            dll_paths.append(os.path.join(sys_prefix, 'DLLs'))
        else:  # Unix/Linux
            lib_paths.append(os.path.join(sys_prefix, 'lib', f'python{sys.version_info.major}.{sys.version_info.minor}'))
            site_packages_paths.append(os.path.join(sys_prefix, 'lib', 
                                   f'python{sys.version_info.major}.{sys.version_info.minor}', 
                                   'site-packages'))
        
        # 原始Python安装的路径
        if os.name == 'nt':  # Windows
            lib_paths.append(os.path.join(prefix, 'Lib'))
            site_packages_paths.append(os.path.join(prefix, 'Lib', 'site-packages'))
            dll_paths.append(os.path.join(prefix, 'DLLs'))
        else:  # Unix/Linux
            lib_paths.append(os.path.join(prefix, 'lib', f'python{sys.version_info.major}.{sys.version_info.minor}'))
            site_packages_paths.append(os.path.join(prefix, 'lib', 
                                   f'python{sys.version_info.major}.{sys.version_info.minor}', 
                                   'site-packages'))
    else:
        # 非虚拟环境
        if os.name == 'nt':  # Windows
            lib_paths.append(os.path.join(sys.prefix, 'Lib'))
            site_packages_paths.append(os.path.join(sys.prefix, 'Lib', 'site-packages'))
            dll_paths.append(os.path.join(sys.prefix, 'DLLs'))
        else:  # Unix/Linux
            lib_paths.append(os.path.join(sys.prefix, 'lib', f'python{sys.version_info.major}.{sys.version_info.minor}'))
            site_packages_paths.append(os.path.join(sys.prefix, 'lib', 
                               f'python{sys.version_info.major}.{sys.version_info.minor}', 
                               'site-packages'))
    
    # 3. 查找系统中的Python安装
    if os.name == 'nt':  # Windows
        for drive in ['C:', 'D:', 'E:', 'F:']:
            # 常见Python安装路径
            for version in ['35', '36', '37', '38', '39', '310', '311', '312']:
                for base in ['Python', 'Program Files\\Python', 'Program Files (x86)\\Python']:
                    path = f"{drive}\\{base}{version}"
                    if os.path.exists(path):
                        lib_paths.append(os.path.join(path, 'Lib'))
                        site_packages_paths.append(os.path.join(path, 'Lib', 'site-packages'))
                        dll_paths.append(os.path.join(path, 'DLLs'))
    
    # 4. DLL路径
    # 添加系统路径中的DLL目录
    for path in os.environ.get('PATH', '').split(os.pathsep):
        if os.path.exists(path):
            dll_paths.append(path)
    
    # 添加Python目录作为DLL路径
    if hasattr(sys, 'executable') and os.path.exists(os.path.dirname(sys.executable)):
        dll_paths.append(os.path.dirname(sys.executable))
    
    # 去重并过滤不存在的路径
    lib_paths = [p for p in list(dict.fromkeys(lib_paths)) if os.path.exists(p)]
    site_packages_paths = [p for p in list(dict.fromkeys(site_packages_paths)) if os.path.exists(p)]
    dll_paths = [p for p in list(dict.fromkeys(dll_paths)) if os.path.exists(p)]
    
    return lib_paths, site_packages_paths, dll_paths

def find_module_path(module_name, lib_paths, site_packages_paths):
    """查找模块的路径"""
    possible_locations = []
    
    # 1. 检查是否是标准库模块
    for lib_path in lib_paths:
        # 检查 .py 文件
        py_file = os.path.join(lib_path, f"{module_name}.py")
        if os.path.exists(py_file):
            possible_locations.append((py_file, "标准库(.py)"))
        
        # 检查目录
        dir_path = os.path.join(lib_path, module_name)
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            # 检查是否有 __init__.py
            init_file = os.path.join(dir_path, "__init__.py")
            if os.path.exists(init_file):
                possible_locations.append((dir_path, "标准库(包)"))
    
    # 2. 检查site-packages
    for site_path in site_packages_paths:
        # 检查 .py 文件
        py_file = os.path.join(site_path, f"{module_name}.py")
        if os.path.exists(py_file):
            possible_locations.append((py_file, "site-packages(.py)"))
            
        # 检查目录
        dir_path = os.path.join(site_path, module_name)
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            # 是否有 __init__.py
            init_file = os.path.join(dir_path, "__init__.py")
            if os.path.exists(init_file):
                possible_locations.append((dir_path, "site-packages(包)"))
                
        # 检查.egg文件/目录和其他格式
        for item in os.listdir(site_path):
            if module_name in item and (item.endswith('.egg') or 
                                      item.endswith('.dist-info') or 
                                      item.endswith('.egg-info')):
                full_path = os.path.join(site_path, item)
                possible_locations.append((full_path, "site-packages(egg/dist-info)"))
    
    # 3. 检查嵌套模块路径 (例如：package.subpackage)
    if '.' in module_name:
        parts = module_name.split('.')
        parent = parts[0]
        child = '.'.join(parts[1:])
        
        # 查找父模块
        parent_locations = find_module_path(parent, lib_paths, site_packages_paths)
        if isinstance(parent_locations, list):
            for parent_loc in parent_locations:
                possible_locations.append(parent_loc)
        elif parent_locations:
            possible_locations.append((parent_locations, f"父模块({parent})"))
            
            # 在父模块中查找子模块
            if os.path.isdir(parent_locations):
                child_path = os.path.join(parent_locations, *parts[1:])
                if os.path.exists(child_path):
                    if os.path.isdir(child_path):
                        possible_locations.append((child_path, f"子模块目录({child})"))
                    else:
                        possible_locations.append((child_path, f"子模块文件({child})"))
                        
                # 还要检查是否有 .py 文件
                child_py = os.path.join(parent_locations, f"{parts[1]}.py")
                if os.path.exists(child_py):
                    possible_locations.append((child_py, f"子模块(.py)({parts[1]})"))
    
    # 如果找到多个位置，提供选择
    if len(possible_locations) > 1:
        print(f"\n发现模块 '{module_name}' 有多个可能的位置:")
        for i, (path, location_type) in enumerate(possible_locations, 1):
            print(f"{i}. {path} ({location_type})")
        
        # 默认选择第一个
        chosen_path, location_type = possible_locations[0]
        print(f"自动选择: {chosen_path} ({location_type})")
        return chosen_path
    
    elif len(possible_locations) == 1:
        path, location_type = possible_locations[0]
        print(f"找到模块 '{module_name}' 位置: {path} ({location_type})")
        return path
    
    # 尝试通过importlib查找
    try:
        spec = importlib.util.find_spec(module_name)
        if spec and spec.origin and os.path.exists(spec.origin):
            print(f"通过importlib找到模块 '{module_name}' 位置: {spec.origin}")
            return spec.origin
    except:
        pass
        
    print(f"警告: 无法找到模块 '{module_name}' 的路径")
    return None

def find_c_extension_module(module_name, dll_paths):
    """查找C扩展模块的路径"""
    # 可能的文件名
    possible_names = []
    
    # 处理Windows和Unix上不同的文件扩展名
    if os.name == 'nt':  # Windows
        possible_names.append(f"{module_name}.pyd")
        possible_names.append(f"{module_name}.dll")
    else:  # Unix
        possible_names.append(f"{module_name}.so")
        possible_names.append(f"lib{module_name}.so")
    
    # 在所有DLL路径中查找
    for dll_path in dll_paths:
        for name in possible_names:
            full_path = os.path.join(dll_path, name)
            if os.path.exists(full_path):
                print(f"找到C扩展模块 '{module_name}': {full_path}")
                return full_path
    
    return None

def find_dependent_dlls(module_name, dll_paths):
    """查找模块依赖的DLL文件"""
    dependent_dlls = []
    
    # 检查特殊模块映射
    if module_name in SPECIAL_MODULE_DLL_MAP:
        dll_patterns = SPECIAL_MODULE_DLL_MAP[module_name]
        for pattern in dll_patterns:
            for dll_path in dll_paths:
                matches = glob.glob(os.path.join(dll_path, pattern))
                dependent_dlls.extend(matches)
    
    return dependent_dlls

def copy_module(module_name, lib_paths, site_packages_paths, dll_paths):
    """复制模块及其依赖DLL"""
    print(f"\n==== 复制模块: {module_name} ====")
    
    # 将点分隔的模块名转换为根模块名
    root_module = module_name.split('.')[0]
    
    module_path = find_module_path(module_name, lib_paths, site_packages_paths)
    if not module_path:
        print(f"找不到模块 {module_name} 的路径")
        return False
    
    print(f"找到模块路径: {module_path}")
    
    # 检查是否为包(目录)
    success = False
    if os.path.isdir(module_path):
        # 这是一个包，复制整个包
        print(f"模块 {module_name} 是一个包，复制整个包...")
        success = copy_full_package(root_module, lib_paths, site_packages_paths)
    else:
        # 先尝试找到并复制根模块作为包
        root_module_path = find_module_path(root_module, lib_paths, site_packages_paths)
        if root_module_path and os.path.isdir(root_module_path):
            # 根模块是一个包，复制整个包
            print(f"模块 {module_name} 属于包 {root_module}，复制整个包...")
            success = copy_full_package(root_module, lib_paths, site_packages_paths)
        else:
            # 单个文件，复制该文件
            dest_file = os.path.join(DESTINATION_PATH, os.path.basename(module_path))
            try:
                os.makedirs(os.path.dirname(dest_file), exist_ok=True)
                shutil.copy2(module_path, dest_file)
                record_copy_operation(module_path, dest_file, "FILE")
                print(f"复制文件: {module_path} -> {dest_file}")
                success = True
            except Exception as e:
                print(f"复制文件失败: {e}")
    
    # 处理模块所需的DLL依赖
    if success and (module_name in SPECIAL_MODULE_DLL_MAP or root_module in SPECIAL_MODULE_DLL_MAP):
        target_module = module_name if module_name in SPECIAL_MODULE_DLL_MAP else root_module
        copy_dependent_dlls(target_module, dll_paths)
    
    return success

def copy_full_package(package_name, lib_paths, site_packages_paths, specific_source_path=None):
    """复制整个包到目标目录"""
    print(f"\n====== 完整复制包: {package_name} ======")
    
    # 如果提供了特定源路径，直接使用
    if specific_source_path and os.path.exists(specific_source_path):
        source_path = specific_source_path
        print(f"使用指定源路径: {source_path}")
    else:
        # 查找包的路径
        source_path = find_module_path(package_name, lib_paths, site_packages_paths)
    
    if not source_path:
        print(f"无法找到包 {package_name} 的路径")
        return False
        
    print(f"找到包 {package_name} 路径: {source_path}")
    
    # 确定目标目录
    if os.path.isdir(source_path):
        # 如果源是目录，目标也是目录
        target_path = os.path.join(DESTINATION_PATH, os.path.basename(source_path))
    else:
        # 如果源是文件，目标也是文件
        target_path = os.path.join(DESTINATION_PATH, os.path.basename(source_path))
        
    # 检查目标是否已存在
    if os.path.exists(target_path):
        print(f"目标目录已存在: {target_path}")
        
        # 使用ask_user来遵循当前模式，而不是直接input
        if not ask_user("是否覆盖?", True):
            print("跳过复制。")
            return False
    
    try:
        # 复制包
        if os.path.isdir(source_path):
            # 递归获取源目录中的所有文件
            all_files = []
            for root, dirs, files in os.walk(source_path):
                for file in files:
                    src_file = os.path.join(root, file)
                    rel_path = os.path.relpath(src_file, source_path)
                    all_files.append((src_file, rel_path))
            
            # 如果目标目录存在，先删除
            if os.path.exists(target_path):
                shutil.rmtree(target_path)
            
            # 创建目标目录
            os.makedirs(target_path, exist_ok=True)
            
            # 复制所有文件
            for src_file, rel_path in all_files:
                dst_file = os.path.join(target_path, rel_path)
                os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                shutil.copy2(src_file, dst_file)
                print(f"复制文件: {src_file} -> {dst_file}")
            
            # 记录整个包的复制操作
            record_copy_operation(source_path, target_path, "PACKAGE")
        else:
            # 单文件处理保持不变
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            shutil.copy2(source_path, target_path)
            record_copy_operation(source_path, target_path, "FILE")
            
        print(f"成功复制 {package_name} 到 {target_path}")
        
        # 复制相关的元数据目录，如 .dist-info 或 .egg-info
        if os.path.isdir(source_path):
            copy_metadata_directories(package_name, site_packages_paths)
        
        return True
    except Exception as e:
        print(f"复制包 {package_name} 时出错: {e}")
        return False

def copy_metadata_directories(package_name, site_packages_paths):
    """复制包的元数据目录，如.dist-info或.egg-info"""
    for site_path in site_packages_paths:
        for item in os.listdir(site_path):
            # 查找与包名相关的元数据目录
            if item.startswith(f"{package_name}-") and (item.endswith('.dist-info') or item.endswith('.egg-info')):
                src_path = os.path.join(site_path, item)
                dst_path = os.path.join(DESTINATION_PATH, item)
                
                try:
                    if os.path.exists(dst_path):
                        if ask_user(f"元数据目录已存在: {dst_path}，是否覆盖?", True):
                            if os.path.isdir(dst_path):
                                shutil.rmtree(dst_path)
                            else:
                                os.remove(dst_path)
                        else:
                            print(f"跳过复制元数据目录: {src_path}")
                            continue
                    
                    if os.path.isdir(src_path):
                        shutil.copytree(src_path, dst_path)
                    else:
                        shutil.copy2(src_path, dst_path)
                    
                    print(f"复制元数据目录: {src_path} -> {dst_path}")
                    record_copy_operation(src_path, dst_path, "METADATA")
                except Exception as e:
                    print(f"复制元数据目录失败: {e}")

def copy_all_full_packages(lib_paths=None, site_packages_paths=None):
    """复制所有常见的完整包"""
    print("\n==== 预先复制常见包 ====")
    
    # 如果没有提供路径参数，获取它们
    if lib_paths is None or site_packages_paths is None:
        lib_paths, site_packages_paths, _ = get_python_paths()
    
    for package in COMMON_FULL_PACKAGES:
        if ask_user(f"\n是否复制完整包 {package}?", False):
            copy_full_package(package, lib_paths, site_packages_paths)

def parse_error_for_modules(error_text):
    """从错误文本中解析出缺少的模块名称"""
    # 首先打印完整错误信息供用户查看
    print("\n==== 捕获到的错误信息 ====")
    print(error_text)
    print("==========================\n")
    
    # 匹配可能的错误模式
    patterns = [
        r"No module named '([^']+)'",
        r"ImportError: cannot import name '([^']+)' from '([^']+)'",
        r"ModuleNotFoundError: No module named '([^']+)'",
        r"ModuleNotFoundError: No module named \"([^\"]+)\"",
        r"ImportError: No module named ([^'\"\s]+)",
        r"ImportError: DLL load failed.+?importing (_[^:\s]+)",  # DLL加载失败
        r"ImportError: DLL load failed while importing ([^:\s]+)", # 另一种DLL错误模式
        r"ImportError: cannot import name '([^']+)'", # 导入名称失败
        r"AttributeError: module '([^']+)' has no attribute '([^']+)'", # 属性错误
    ]
    
    missing_modules = []
    
    for pattern in patterns:
        matches = re.finditer(pattern, error_text)
        for match in matches:
            if "module '([^']+)' has no attribute '([^']+)'" in pattern:
                # 属性错误，添加模块名
                module_name = match.group(1)
                missing_modules.append(module_name)
            elif "cannot import name '([^']+)' from '([^']+)'" in pattern:
                # 名称导入失败，添加模块
                module_name = match.group(2)
                missing_modules.append(module_name)
            elif len(match.groups()) == 1:
                module_name = match.group(1)
                # 保留原始模块名
                missing_modules.append(module_name)
                # 同时添加可能的父模块
                if '.' in module_name:
                    root_module = module_name.split('.')[0]
                    missing_modules.append(root_module)
    
    # 去重
    unique_modules = list(set(missing_modules))
    
    if unique_modules:
        print(f"从错误中识别出可能缺少的模块: {', '.join(unique_modules)}")
    else:
        print("无法从错误中识别出缺少的模块")
        
        # 如果没有识别出模块，尝试通过更基本的方式查找可能的模块名
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', error_text)
        potential_modules = [w for w in words if len(w) > 2 and not w.isupper() and w not in 
                           ['import', 'from', 'module', 'name', 'error', 'failed', 'while', 'the', 'File']]
        
        if potential_modules:
            print("以下单词可能是模块名，您可以手动检查:")
            for module in potential_modules[:10]:  # 限制显示数量
                print(f"- {module}")
    
    return unique_modules

def ask_user(prompt, default_yes=True):
    """根据当前模式，决定是否询问用户"""
    if AUTO_MODE:
        print(f"{prompt} [自动选择: {'是' if default_yes else '否'}]")
        return default_yes
    else:
        response = input(f"{prompt} (y/n): ").strip().lower()
        return response == 'y' or (response == '' and default_yes)

def generate_dependency_manifest(fixed_modules, lib_paths, site_packages_paths, dll_paths):
    """生成依赖清单文件，记录所有已复制的模块"""
    global COPY_OPERATIONS
    
    manifest_file = os.path.join(os.path.dirname(DESTINATION_PATH), "dependency_manifest.txt")
    
    print(f"\n生成依赖清单文件: {manifest_file}")
    
    try:
        with open(manifest_file, 'w', encoding='utf-8') as f:
            f.write("# Python模块依赖清单\n")
            f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 目标目录: {DESTINATION_PATH}\n\n")
            
            f.write("# 系统信息\n")
            info = get_system_info()
            for key, value in info.items():
                f.write(f"# {key}: {value}\n")
            f.write("\n")
            
            f.write("# 复制的模块列表\n")
            for module in sorted(fixed_modules):
                f.write(f"{module}\n")
                
                # 尝试获取模块的源路径
                module_path = find_module_path(module, lib_paths, site_packages_paths)
                if module_path:
                    f.write(f"# 源路径: {module_path}\n")
                    
                # 如果是特殊模块，列出DLL依赖
                if module in SPECIAL_MODULE_DLL_MAP:
                    f.write("# DLL依赖:\n")
                    for dll in SPECIAL_MODULE_DLL_MAP[module]:
                        f.write(f"#   {dll}\n")
                
                f.write("\n")
            
            # 添加所有复制操作记录
            f.write("# 复制操作记录\n")
            for op in COPY_OPERATIONS:
                # 格式: [操作类型] 源路径 -> 目标路径
                f.write(f"COPY: {op['type']}:{op['source']} -> {op['destination']}\n")
            
            f.write("\n# 复制命令示例 (Windows)\n")
            f.write("# 使用以下命令可以手动复制这些依赖:\n")
            f.write("# xcopy /E /I \"源路径\\模块名\" \"目标路径\\模块名\"\n")
            
            f.write("\n# 复制命令示例 (Linux/macOS)\n")
            f.write("# 使用以下命令可以手动复制这些依赖:\n")
            f.write("# cp -r \"源路径/模块名\" \"目标路径/模块名\"\n")
            
        print(f"依赖清单生成成功！可以在 {manifest_file} 中查看所有复制的模块信息。")
        return True
    except Exception as e:
        print(f"生成依赖清单时出错: {e}")
        return False

def parse_manifest_file(manifest_path):
    """从清单文件中解析模块信息和复制操作"""
    if not os.path.exists(manifest_path):
        print(f"错误: 清单文件不存在: {manifest_path}")
        return {}, []
    
    modules_info = {}
    copy_operations = []
    current_module = None
    
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                
                # 跳过注释和空行
                if not line or line.startswith('#'):
                    continue
                
                # 处理复制操作记录
                if line.startswith('COPY: '):
                    op_info = line[6:].strip()
                    try:
                        op_type, paths = op_info.split(':', 1)
                        source, dest = paths.split(' -> ')
                        copy_operations.append({
                            'type': op_type,
                            'source': source,
                            'destination': dest
                        })
                    except:
                        print(f"警告: 无法解析复制操作记录: {line}")
                    continue
                
                # 如果没有缩进，则认为是模块名
                if not line.startswith(' '):
                    current_module = line
                    modules_info[current_module] = {'source_path': None, 'dlls': []}
                
                # 检查是否是源路径行
                elif current_module and '# 源路径:' in line:
                    source_path = line.split('# 源路径:')[1].strip()
                    modules_info[current_module]['source_path'] = source_path
                
                # 检查是否是DLL依赖行
                elif current_module and line.startswith('#   '):
                    dll_name = line.replace('#', '').strip()
                    if dll_name:
                        modules_info[current_module]['dlls'].append(dll_name)
    
    except Exception as e:
        print(f"解析清单文件时出错: {e}")
        return {}, []
    
    return modules_info, copy_operations

def recover_from_manifest(manifest_path, destination_path=None):
    """从清单文件恢复所有模块和文件"""
    global DESTINATION_PATH
    
    # 保存原始目标路径
    original_destination = DESTINATION_PATH
    
    if destination_path:
        print(f"使用指定的目标目录: {destination_path}")
        DESTINATION_PATH = destination_path
    
    print(f"\n==== 从清单文件恢复模块 ====")
    print(f"清单文件: {manifest_path}")
    print(f"目标目录: {DESTINATION_PATH}")
    
    # 确保目标目录存在
    os.makedirs(DESTINATION_PATH, exist_ok=True)
    
    # 解析清单文件
    modules_info, copy_operations = parse_manifest_file(manifest_path)
    
    if not modules_info and not copy_operations:
        print("清单文件中没有找到有效的信息")
        # 恢复原始目标路径
        DESTINATION_PATH = original_destination
        return False
    
    print(f"从清单中发现 {len(modules_info)} 个模块和 {len(copy_operations)} 个复制操作")
    
    # 获取Python路径用于查找模块
    lib_paths, site_packages_paths, dll_paths = get_python_paths()
    
    # 直接从复制操作恢复
    if copy_operations:
        print("\n==== 从复制操作记录恢复 ====")
        success_count = 0
        
        for op in copy_operations:
            op_type = op['type']
            source = op['source']
            dest = op['destination']
            
            # 如果目标路径在原始清单指定的目录中，调整为新的目标目录
            if dest.startswith(original_destination):
                relative_path = os.path.relpath(dest, original_destination)
                new_dest = os.path.join(DESTINATION_PATH, relative_path)
            else:
                # 否则直接使用目标路径中的文件名部分
                new_dest = os.path.join(DESTINATION_PATH, os.path.basename(dest))
                
            print(f"\n处理复制操作: [{op_type}] {source} -> {new_dest}")
            
            # 检查源文件是否存在
            if not os.path.exists(source):
                print(f"源文件不存在: {source}")
                print("尝试查找替代文件...")
                
                # 尝试在当前环境中查找文件
                basename = os.path.basename(source)
                found = False
                
                for search_path in lib_paths + site_packages_paths + dll_paths:
                    alt_source = os.path.join(search_path, basename)
                    if os.path.exists(alt_source):
                        source = alt_source
                        print(f"找到替代文件: {source}")
                        found = True
                        break
                
                if not found:
                    print(f"无法找到替代文件: {basename}")
                    continue
            
            # 执行复制
            try:
                if op_type == "PACKAGE" and os.path.isdir(source):
                    # 复制整个包
                    if os.path.exists(new_dest):
                        if ask_user(f"目标目录已存在: {new_dest}，是否覆盖?", True):
                            shutil.rmtree(new_dest)
                        else:
                            print("跳过复制。")
                            continue
                    
                    # 使用我们之前加强的文件复制方法
                    os.makedirs(new_dest, exist_ok=True)
                    for root, dirs, files in os.walk(source):
                        for file in files:
                            src_file = os.path.join(root, file)
                            rel_path = os.path.relpath(src_file, source)
                            dst_file = os.path.join(new_dest, rel_path)
                            os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                            shutil.copy2(src_file, dst_file)
                    
                    print(f"已复制包: {source} -> {new_dest}")
                    success_count += 1
                else:
                    # 单个文件复制
                    os.makedirs(os.path.dirname(new_dest), exist_ok=True)
                    shutil.copy2(source, new_dest)
                    print(f"已复制文件: {source} -> {new_dest}")
                    success_count += 1
            except Exception as e:
                print(f"复制失败: {e}")
        
        print(f"\n成功执行了 {success_count}/{len(copy_operations)} 个复制操作")
        
        # 恢复原始目标路径
        DESTINATION_PATH = original_destination
        return success_count > 0
    
    # 如果没有复制操作记录，回退到按模块恢复
    # ... 现有代码 ...

def copy_dependent_dlls(module_name, dll_paths):
    """复制模块依赖的DLL文件"""
    print(f"\n==== 复制模块 {module_name} 依赖的DLL文件 ====")
    
    if module_name not in SPECIAL_MODULE_DLL_MAP:
        print(f"模块 {module_name} 没有定义特殊DLL依赖")
        return False
    
    dll_list = SPECIAL_MODULE_DLL_MAP[module_name]
    if not dll_list:
        print(f"模块 {module_name} 的DLL依赖列表为空")
        return True  # 返回True因为空列表意味着没有依赖需要复制
    
    success_count = 0
    for dll_pattern in dll_list:
        found_dlls = find_dll_files(dll_pattern, dll_paths)
        
        if not found_dlls:
            print(f"警告: 无法找到DLL: {dll_pattern}")
            continue
        
        # 复制所有找到的DLL
        for dll_path in found_dlls:
            dll_name = os.path.basename(dll_path)
            dest_path = os.path.join(DESTINATION_PATH, dll_name)
            
            try:
                if os.path.exists(dest_path):
                    if ask_user(f"DLL已存在: {dest_path}，是否覆盖?", True):
                        shutil.copy2(dll_path, dest_path)
                        # 记录DLL复制操作
                        record_copy_operation(dll_path, dest_path, "DLL")
                        print(f"已覆盖DLL: {dll_path} -> {dest_path}")
                        success_count += 1
                else:
                    shutil.copy2(dll_path, dest_path)
                    # 记录DLL复制操作
                    record_copy_operation(dll_path, dest_path, "DLL")
                    print(f"已复制DLL: {dll_path} -> {dest_path}")
                    success_count += 1
            except Exception as e:
                print(f"复制DLL失败: {e}")
    
    if success_count > 0:
        print(f"成功复制了 {success_count} 个DLL文件")
        return True
    else:
        print(f"没有复制任何DLL文件")
        return False

def find_dll_files(dll_pattern, dll_paths):
    """查找匹配模式的DLL文件"""
    found_files = []
    
    # 在所有DLL路径中查找匹配的文件
    for path in dll_paths:
        if not os.path.exists(path):
            continue
            
        # 支持通配符模式
        if '*' in dll_pattern:
            pattern_path = os.path.join(path, dll_pattern)
            matches = glob.glob(pattern_path)
            found_files.extend(matches)
        else:
            # 精确文件名
            file_path = os.path.join(path, dll_pattern)
            if os.path.exists(file_path):
                found_files.append(file_path)
    
    # 去重
    found_files = list(set(found_files))
    
    if found_files:
        print(f"找到 {len(found_files)} 个匹配 '{dll_pattern}' 的文件:")
        for file in found_files[:5]:  # 只显示前5个
            print(f"- {file}")
        if len(found_files) > 5:
            print(f"  ...以及其他 {len(found_files)-5} 个文件")
    
    return found_files

def copy_module_with_dependencies(module_name, lib_paths, site_packages_paths, dll_paths, visited=None):
    """复制模块及其依赖"""
    if visited is None:
        visited = set()
    
    if module_name in visited:
        return True
    
    visited.add(module_name)
    print(f"\n复制模块及其依赖: {module_name}")
    
    # 首先复制主模块
    success = copy_module(module_name, lib_paths, site_packages_paths, dll_paths)
    
    # 检查是否有特定依赖需要复制
    if module_name in PACKAGE_DEPENDENCIES:
        for dependency in PACKAGE_DEPENDENCIES[module_name]:
            if dependency not in visited:
                print(f"\n处理 {module_name} 的依赖: {dependency}")
                copy_module_with_dependencies(dependency, lib_paths, site_packages_paths, dll_paths, visited)
    
    return success

def main():
    """主函数"""
    global AUTO_MODE, COMPILED_EXE_PATH, DESTINATION_PATH
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='自动复制Python模块到编译结果目录')
    parser.add_argument('--auto', action='store_true', help='全自动模式，不询问确认')
    parser.add_argument('--interactive', action='store_true', help='交互模式，每步都询问确认')
    parser.add_argument('--exe', help='编译后的可执行文件路径')
    parser.add_argument('--dest', help='目标目录路径')
    parser.add_argument('--show-error', action='store_true', help='显示详细错误信息')
    parser.add_argument('--from-manifest', help='从指定清单文件恢复模块')
    args = parser.parse_args()
    
    # 设置模式
    if args.interactive:
        AUTO_MODE = False
    elif args.auto:
        AUTO_MODE = True
    
    # 更新路径
    if args.exe:
        COMPILED_EXE_PATH = args.exe
    if args.dest:
        DESTINATION_PATH = args.dest
        
    # 如果指定了从清单恢复
    if args.from_manifest:
        return recover_from_manifest(args.from_manifest, args.dest)
    
    # 显示当前模式
    print(f"\n当前运行模式: {'全自动模式' if AUTO_MODE else '交互确认模式'}")
    
    # 显示系统信息
    system_info = get_system_info()
    print("\n==== 系统信息 ====")
    for key, value in system_info.items():
        print(f"{key}: {value}")
    
    # 获取Python路径
    lib_paths, site_packages_paths, dll_paths = get_python_paths()
    
    print("\n==== 搜索路径 ====")
    print("标准库路径:")
    for path in lib_paths:
        print(f"- {path}")
    
    print("\nsite-packages路径:")
    for path in site_packages_paths:
        print(f"- {path}")
    
    print("\nDLL搜索路径:")
    for path in dll_paths[:5]:  # 只显示前5个，避免太长
        print(f"- {path}")
    if len(dll_paths) > 5:
        print(f"...以及其他 {len(dll_paths)-5} 个路径")
    
    print(f"\n编译程序路径: {COMPILED_EXE_PATH}")
    print(f"目标目录: {DESTINATION_PATH}")
    
    # 确认路径是否正确 - 使用ask_user
    if not AUTO_MODE:  # 在自动模式下跳过确认
        print("\n请检查以上路径是否正确。如果不正确，请修改脚本中的路径变量。")
        print("按Enter键继续...", end="")
        input()
    
    # 询问是否预先复制常见包 - 使用ask_user
    if ask_user("\n是否要预先复制常见完整包?", True):
        copy_all_full_packages(lib_paths, site_packages_paths)
    
    # 主循环 - 尝试启动程序并处理缺少的模块
    attempt = 0
    fixed_modules = set()
    
    while attempt < MAX_ATTEMPTS:
        attempt += 1
        print(f"\n==== 尝试 {attempt}/{MAX_ATTEMPTS} ====")
        
        # 启动编译后的程序并捕获错误输出
        try:
            print(f"启动程序: {COMPILED_EXE_PATH}")
            process = subprocess.Popen(
                COMPILED_EXE_PATH, 
                stderr=subprocess.PIPE,
                stdout=subprocess.PIPE,
                text=True,
                encoding='utf-8',  # 使用UTF-8编码
                errors='replace'    # 替换无法解码的字符
            )
            
            # 等待一小段时间，允许错误出现
            try:
                print(f"等待程序运行 {TIMEOUT} 秒...")
                stdout, stderr = process.communicate(timeout=TIMEOUT)
            except subprocess.TimeoutExpired:
                print(f"程序运行超过 {TIMEOUT} 秒而没有退出，可能工作正常或遇到其他问题")
                process.terminate()
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    process.kill()
                break
        
        except Exception as e:
            print(f"运行程序时出错: {e}")
            break
        
        # 检查程序是否有错误输出
        if not stderr:
            print("程序没有错误输出，可能已成功启动。")
            if stdout:
                print("输出信息:", stdout[:200] + ("..." if len(stdout) > 200 else ""))
            
            # 生成依赖清单
            print("\n程序成功启动，正在生成依赖清单...")
            generate_dependency_manifest(fixed_modules, lib_paths, site_packages_paths, dll_paths)
            break
        
        # 解析错误以查找缺少的模块
        missing_modules = parse_error_for_modules(stderr)
        
        if not missing_modules:
            print("没有找到缺少的模块信息。")
            
            # 询问是否手动输入模块名 - 使用ask_user
            if ask_user("\n是否要手动输入模块名?", False):
                print("请输入模块名称 (如果有多个，用逗号分隔): ", end="")
                manual_modules = input().strip()
                if manual_modules:
                    missing_modules = [m.strip() for m in manual_modules.split(',')]
                    print(f"手动添加的模块: {', '.join(missing_modules)}")
            else:
                break
        
        # 处理缺少的模块
        if missing_modules:
            print(f"\n发现可能缺少的模块: {', '.join(missing_modules)}")
            
            modules_fixed = False
            for module in missing_modules:
                if module not in fixed_modules:
                    print(f"\n正在处理模块: {module}")
                    # 使用带依赖关系的复制函数
                    success = copy_module_with_dependencies(module, lib_paths, site_packages_paths, dll_paths)
                    if success:
                        fixed_modules.add(module)
                        modules_fixed = True
            
            if not modules_fixed:
                print("无法修复更多模块，退出循环")
                break
            
            print(f"\n本次修复的模块: {', '.join(module for module in missing_modules if module in fixed_modules)}")
            print("重新启动程序以检查是否还有缺少的模块...")
        else:
            break
    
    if attempt >= MAX_ATTEMPTS:
        print(f"达到最大尝试次数 ({MAX_ATTEMPTS})，退出")
    
    print("\n==== 自动复制模块完成 ====")
    print(f"共复制了 {len(fixed_modules)} 个模块:")
    for module in sorted(fixed_modules):
        print(f"- {module}")
    
    # 生成依赖清单
    if fixed_modules:
        generate_dependency_manifest(fixed_modules, lib_paths, site_packages_paths, dll_paths)
    
    print("\n如果程序仍无法运行，可能需要手动复制其他依赖或检查程序的其他问题。")

if __name__ == "__main__":
    # 添加使用说明
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("""
复制缺失模块工具 - 帮助自动复制Python模块依赖

使用方法:
  python copy_missing_modules.py [选项]

选项:
  --auto               全自动模式，不询问确认
  --interactive        交互模式，每步都询问确认
  --exe PATH           指定编译后的可执行文件路径
  --dest PATH          指定目标目录路径
  --show-error         显示详细错误信息
  --from-manifest PATH 从指定清单文件恢复模块

示例:
  python copy_missing_modules.py --auto --exe "./out/app.exe" --dest "./out/"
  python copy_missing_modules.py --interactive
  python copy_missing_modules.py --from-manifest "./out/dependency_manifest.txt" --dest ".//out/main.dist/"

默认模式为全自动模式。
        """)
        sys.exit(0)
    
    main()