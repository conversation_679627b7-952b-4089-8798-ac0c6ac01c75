#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import hashlib
import json
import hmac
from pathlib import Path
import argparse
import time
import re

# 签名密钥 - 生产环境应使用更安全的密钥管理
SIGNATURE_KEY = b"PVV_8f2a61a4e9b94a31b8ea8d6e2f13f308_SECRET_KEY"

def calculate_file_hash(file_path):
    """计算文件的SHA-256哈希值"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def generate_critical_files_code(directory, output_file, critical_patterns=None):
    """生成关键文件哈希并更新到代码中"""
    # 下面是关键代码用来生成完整性代码
    if critical_patterns is None:

        critical_patterns = [
            "index.html",
            "assets/*.css",
            "assets/*.js"
        ]
        # critical_patterns = [
        #     "index.html",
        #     "assets/*.css",
        #     "assets/*.js"
        # ]
    
    # 扫描匹配的关键文件
    critical_files = {}
    
    print(f"查找关键文件...")
    
    for pattern in critical_patterns:
        # 如果模式包含通配符
        if '*' in pattern or '?' in pattern:
            base_dir = os.path.dirname(pattern) if os.path.dirname(pattern) else ''
            file_pattern = os.path.basename(pattern)
            
            # 转换通配符模式为正则表达式
            regex_pattern = '^' + file_pattern.replace('.', '\\.').replace('*', '.*').replace('?', '.') + '$'
            regex = re.compile(regex_pattern)
            
            # 搜索匹配文件
            search_dir = os.path.join(directory, base_dir)
            if os.path.exists(search_dir):
                for file in os.listdir(search_dir):
                    if regex.match(file):
                        file_path = os.path.join(base_dir, file)
                        full_path = os.path.join(directory, file_path)
                        critical_files[file_path] = calculate_file_hash(full_path)
                        print(f"匹配到文件: {file_path}")
        else:
            # 直接查找指定文件
            full_path = os.path.join(directory, pattern)
            if os.path.exists(full_path):
                critical_files[pattern] = calculate_file_hash(full_path)
                print(f"添加关键文件: {pattern}")
    
    # 同时也生成常规的清单文件(包含所有文件)
    all_files = {}
    for path in Path(directory).rglob('*'):
        if path.is_file() and path.name != ".integrity":
            relative_path = str(path.relative_to(directory))
            all_files[relative_path] = calculate_file_hash(path)
    
    # 保存常规清单(带签名)
    manifest_path = os.path.join(directory, ".integrity")
    json_data = json.dumps(all_files, sort_keys=True).encode('utf-8')
    signature = hmac.new(SIGNATURE_KEY, json_data, hashlib.sha256).digest()
    
    with open(manifest_path, 'wb') as f:
        f.write(signature + json_data)
    
    print(f"已生成常规完整性清单，包含 {len(all_files)} 个文件")
    
    # 更新代码中的硬编码哈希
    if critical_files:
        # 读取目标代码文件
        with open(output_file, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 构造新的哈希字典
        hashes_str = "{\n"
        for file_path, hash_value in critical_files.items():
            # 确保路径使用正斜杠，避免Windows反斜杠导致的转义问题
            safe_path = file_path.replace('\\', '/')
            hashes_str += f'    "{safe_path}": "{hash_value}",\n'
        hashes_str += "}"
        
        # 使用非正则表达式方法替换
        dict_start = code.find("CRITICAL_FILE_HASHES = {")
        if dict_start != -1:
            # 找到字典的结束位置（考虑可能的嵌套）
            dict_end = dict_start + len("CRITICAL_FILE_HASHES = {")
            brace_count = 1
            while brace_count > 0 and dict_end < len(code):
                if code[dict_end] == '{':
                    brace_count += 1
                elif code[dict_end] == '}':
                    brace_count -= 1
                dict_end += 1
            
            # 替换字典
            if dict_end <= len(code):
                new_dict = f"CRITICAL_FILE_HASHES = {hashes_str}"
                code = code[:dict_start] + new_dict + code[dict_end:]
                
                # 写回文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(code)
                
                print(f"已更新 {output_file} 中的关键文件哈希值，共 {len(critical_files)} 个文件")
            else:
                print("警告: 无法找到字典的结束位置")
        else:
            print("警告: 在文件中未找到 CRITICAL_FILE_HASHES 常量")
    else:
        print("警告: 未找到任何关键文件!")

def main():
    parser = argparse.ArgumentParser(description='为构建生成完整性检查代码')
    parser.add_argument('--dir', type=str, default='./statics',
                        help='静态文件所在目录 (默认: ./statics)')
    parser.add_argument('--output', type=str, default='./PVV.py',
                        help='要更新的主程序文件 (默认: ./PVV.py)')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.dir):
        print(f"错误: 目录不存在: {args.dir}")
        return 1
    
    if not os.path.isfile(args.output):
        print(f"错误: 输出文件不存在: {args.output}")
        return 1
    
    generate_critical_files_code(args.dir, args.output)
    return 0

if __name__ == "__main__":
    exit(main())

    # 用来修改main中的hash为校验后的结果，保证statics正确不被修改。