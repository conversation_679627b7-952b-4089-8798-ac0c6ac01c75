import{_ as el,r as B,br as fe,c as lt,w as tl,ap as nt,o as ll,E as m,aP as nl,b as _,m as b,e as d,a9 as il,d as l,g as s,t as sl,v as w,B as ol,C as y,aa as U,s as al,V as rl,R as N,S as x,n as le,F as j,c2 as dl,J as cl,h as ne,av as M,a1 as pe,bH as ul,M as W,p as T,bg as it,bM as fl,bk as pl,ae as ml,af as gl,U as st,bj as bl,ac as vl,a5 as Fe,aR as _l,ag as hl,a6 as $e,ca as yl,aI as wl,K as Cl,k as Al,j as Vl,aB as Bl,aC as jl,q as $l,aD as Ol,aE as kl,y as Dl,x as ot,ai as H,Y as at}from"./entry-C_c_Ffav.js";/* empty css                   *//* empty css                    *//* empty css                     *//* empty css               *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css                 *//* empty css                 */const El={class:"a2b-container"},Sl={class:"app-header"},Nl={class:"header-top"},xl={class:"header-title"},zl={class:"design-info"},Ul={class:"design-history-panel"},Jl={class:"panel-header"},Fl={class:"history-search"},Tl={class:"history-list-container"},Ll={key:0,class:"empty-history"},Ml={key:1,class:"history-list"},Pl=["onClick"],Rl={class:"item-content"},ql={class:"item-title"},Gl={class:"item-meta"},Hl={class:"item-timestamp"},Ql={class:"item-info"},Yl={class:"item-actions"},Wl={class:"header-actions"},Xl={class:"config-manager-panel"},Zl={class:"panel-header"},Il={class:"panel-actions"},Kl={class:"config-search"},en={class:"config-list-container"},tn={key:0,class:"empty-config"},ln={key:1,class:"config-list"},nn=["onClick"],sn={class:"item-content"},on={class:"item-title"},an={class:"item-meta"},rn={class:"item-description"},dn={class:"item-info"},cn={class:"item-actions"},un={class:"app-content"},fn={class:"bridge-layout"},pn={class:"scene-panel scene-a"},mn={class:"panel-content"},gn={class:"json-editor"},bn={class:"field-header"},vn={class:"field-content"},_n={key:1,class:"array-editor"},hn={key:2,class:"object-array-editor"},yn={class:"object-header"},wn={class:"prop-key"},Cn={key:3,class:"object-editor"},An={class:"prop-header"},Vn={class:"prop-key"},Bn={class:"scene-panel scene-b"},jn={class:"panel-content"},$n={class:"json-editor"},On={class:"field-header"},kn={class:"field-content"},Dn={key:1,class:"array-editor"},En={key:2,class:"object-array-editor"},Sn={class:"object-header"},Nn={class:"prop-key"},xn={key:3,class:"object-editor"},zn={class:"prop-header"},Un={class:"prop-key"},Jn={class:"drawer-content"},Fn={class:"drawer-header"},Tn={class:"section-title"},Ln={class:"guidelines-content"},Mn={class:"guideline-header"},Pn={class:"guideline-title"},Rn={class:"guideline-description"},qn={class:"guideline-actions"},Gn={class:"import-dialog-content"},Hn={key:0,class:"import-error"},Qn={class:"dialog-footer"},Yn={class:"dialog-custom-header"},Wn=["id"],Xn={class:"guideline-form"},Zn={class:"form-header-section"},In={class:"form-scrollable-content"},Kn={class:"form-section"},ei={class:"guideline-dimensions"},ti={class:"dimension-header"},li={class:"dimension-name"},ni={class:"dimension-question"},ii={class:"dimension-content"},si={class:"form-section"},oi={class:"dialog-custom-footer"},ai={class:"template-design-info",style:{"margin-bottom":"15px"}},ri={class:"dialog-footer"},di={class:"dialog-footer"},ci={class:"import-dialog-content"},ui={key:0,class:"import-error"},fi={class:"dialog-footer"},pi={class:"dialog-custom-header"},mi=["id"],gi={class:"config-editor-form"},bi={class:"form-header-section"},vi={class:"form-scrollable-content"},_i={class:"form-section"},hi={class:"fields-list"},yi={class:"field-order-badge"},wi={class:"field-header"},Ci={class:"field-name-type"},Ai={class:"field-actions"},Vi={key:0,class:"field-children"},Bi={key:0,class:"empty-children"},ji={class:"field-order-badge child-badge"},$i={class:"field-header"},Oi={class:"field-name-type"},ki={class:"field-actions"},Di={key:0,class:"nested-children"},Ei={key:0,class:"empty-children"},Si={class:"field-order-badge nested-badge"},Ni={class:"field-header"},xi={class:"field-name-type"},zi={class:"field-actions"},Ui={class:"dialog-custom-footer"},Ji={__name:"A2B",setup(Fi){const S=B(!1),O=B([]),L=B(null),q=B(!1),Ae=B(""),o=fe({id:"scene_default",name:"剧情无痕设计",description:"",bridgeA:{"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},bridgeB:{"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},guidelines:[]}),Te=lt(()=>{if(!Ae.value.trim())return O.value;const t=Ae.value.toLowerCase().trim();return O.value.filter(e=>{const n=(e.name||"").toLowerCase().includes(t),i=(e.description||"").toLowerCase().includes(t),a=Object.keys(e.bridgeA||{}).some(c=>c.toLowerCase().includes(t));return n||i||a})}),G=B([]),Oe=B(""),Ve=B(""),Le=lt(()=>{if(!Ve.value.trim())return G.value;const t=Ve.value.toLowerCase().trim();return G.value.filter(e=>{const n=(e.name||"").toLowerCase().includes(t),i=(e.description||"").toLowerCase().includes(t),a=(e.guidelines||[]).some(c=>(c.title||"").toLowerCase().includes(t)||(c.description||"").toLowerCase().includes(t));return n||i||a})}),ie=B(!1),ke=B(null),J=fe({name:"",configId:""}),se=B(!1),De=B(null),z=fe({name:"",description:""}),X=B(!1),D=fe({id:"",name:"",description:"",bridgeA:{},bridgeB:{}}),u=fe({}),me=B(!1);function rt(){me.value=!me.value}const Z=B(!1),oe=B(!1),Be=B(-1),A=fe({type:"线索铺垫",title:"",description:"",priority:"normal",answers:{physicalCondition:"",motivation:"",obstacle:"",reasonableness:"",implementation:""}}),dt=[{key:"physicalCondition",dimension:"物理条件",question:"道具/环境是否就位？",placeholder:"例：太监服必须存在且可获取"},{key:"motivation",dimension:"行为动机+时机",question:"何必须现在做？",placeholder:"例：为什么角色此时必须这样做？"},{key:"obstacle",dimension:"阻碍清除方案",question:"为何不能选其他方案？",placeholder:"例：不穿太监服无法混入皇宫"},{key:"reasonableness",dimension:"对比设计",question:"如何让读者认同？",placeholder:"例：对比其他选择的劣势"},{key:"implementation",dimension:"如何达成",question:"具体如何铺垫实现？",placeholder:"例：在前文提及太监服的获取途径"}];function ct(){A.type="线索铺垫",A.title="",A.description="",A.priority="normal",Object.keys(A.answers).forEach(t=>{A.answers[t]=""}),oe.value=!1,Be.value=-1,Z.value=!0}function ut(){if(!A.title.trim()){m.warning("请输入铺垫标题");return}if(!A.description.trim()){m.warning("请输入完整描述");return}const t=Object.values(A.answers).filter(n=>n.trim()).join(`

`);A.description.trim()===""&&t&&(A.description=t);const e={type:A.type,title:A.title,description:A.description,priority:A.priority,answers:{...A.answers}};oe.value&&Be.value>=0?(o.guidelines[Be.value]=e,m.success("铺垫更新成功")):(o.guidelines.push(e),m.success("铺垫添加成功")),re(),Z.value=!1}const ge=B(!1),be=B(""),Q=B(""),ae=B(!1),ve=B(!1),_e=B(""),I=B(""),he=B(!1),K=B(null);tl(()=>[o.bridgeA,o.bridgeB,o.guidelines],()=>{q.value=!0,K.value&&clearTimeout(K.value),K.value=setTimeout(async()=>{await re()},5e3)},{deep:!0}),nt(()=>{K.value&&clearTimeout(K.value)});function ft(t){if(!t)return"未知时间";const e=new Date(t),n=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),c=String(e.getHours()).padStart(2,"0"),v=String(e.getMinutes()).padStart(2,"0");return`${n}-${i}-${a} ${c}:${v}`}function Me(){J.name="",J.configId=o.id,ie.value=!0}function pt(){at(()=>{ke.value&&ke.value.focus()})}async function Ee(){if(!J.name.trim()){m.warning("请输入设计名称");return}try{S.value=!0;const t=O.value.find(n=>n.id===J.configId)||O.value[0];if(!t){m.error("未找到有效的配置模板"),S.value=!1;return}const e={id:"design_"+Date.now(),name:J.name.trim(),description:"",timestamp:Date.now(),bridgeA:JSON.parse(JSON.stringify(t.bridgeA||t.sceneA||{})),bridgeB:JSON.parse(JSON.stringify(t.bridgeB||t.sceneB||{})),guidelines:[],isDesign:!0};Object.assign(o,e),L.value=e.id,await de(!0),ie.value=!1,m.success(`新设计"${e.name}"已创建并保存到您的设计列表中`)}catch(t){console.error("创建新设计失败",t),m.error("创建新设计失败: "+(t.message||"未知错误"))}finally{S.value=!1}}function mt(){z.name=o.name||"",z.description=o.description||"",at(()=>{De.value&&De.value.focus()})}async function Se(){if(!z.name.trim()){m.warning("请输入设计名称");return}try{o.name=z.name.trim(),o.description=z.description.trim(),await de(!0),se.value=!1,m.success("设计已保存")}catch(t){console.error("保存设计失败",t),m.error("保存设计失败: "+(t.message||"未知错误"))}}async function Ne(){try{S.value=!0;const t=await window.pywebview.api.book_controller.get_scene_designs();t.success&&t.data?(G.value=t.data.sort((e,n)=>(n.timestamp||0)-(e.timestamp||0)),console.log("从后端API加载历史设计列表成功:",G.value)):(console.warn("后端API返回的设计历史为空:",t),G.value=[])}catch(t){console.error("加载历史设计列表失败",t),m.error("加载历史设计列表失败: "+(t.message||"未知错误")),G.value=[]}finally{S.value=!1}}async function gt(t){try{await H.confirm("确定要删除这个设计吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),await bt(t)}catch(e){if(e==="cancel")return;console.error("确认删除设计失败:",e)}}async function bt(t){try{const e=await window.pywebview.api.book_controller.delete_scene_design(t);if(!e.success)throw new Error(e.message||"删除失败：API调用出错");await Ne(),t===L.value&&(G.value.length>0?await ze(G.value[0].id):Pe()),m.success("设计已删除")}catch(e){console.error("删除设计失败:",e),m.error(e.message||"删除设计失败")}}function Pe(){o.id="scene_default",o.name="剧情无痕设计",o.description="",o.bridgeA={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},o.bridgeB={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},o.guidelines=[],L.value="scene_default",q.value=!1}function vt(){let t="新建模板",e=t,n=1;for(;O.value.some(a=>a.name===e);)e=`${t} ${n}`,n++;const i={id:"config_"+Date.now(),name:e,description:"用于剧情设计的结构模板",bridgeA:JSON.parse(JSON.stringify(o.bridgeA)),bridgeB:JSON.parse(JSON.stringify(o.bridgeB))};Xe(i.id,i)}function _t(t){try{q.value?H.confirm("当前有未保存的更改，切换配置将丢失这些更改。是否继续？","未保存的更改",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then(()=>{xe(t)}).catch(()=>{}):xe(t)}catch(e){console.error("选择配置失败:",e),m.error("选择配置失败: "+(e.message||"未知错误"))}}function xe(t){const e=O.value.find(n=>n.id===t);e&&(Object.assign(o,{id:e.id,name:e.name,description:e.description,bridgeA:JSON.parse(JSON.stringify(e.bridgeA||{})),bridgeB:JSON.parse(JSON.stringify(e.bridgeB||{})),guidelines:[]}),L.value=e.id,q.value=!1,m.success(`已加载模板: ${e.name}`))}async function ht(t){try{const e=O.value.find(n=>n.id===t);if(!e){m.error("未找到指定的模板");return}J.configId=t,J.name="",m.info(`您正在基于模板"${e.name||"未命名模板"}"创建新的设计`),ie.value=!0}catch(e){console.error("选择模板失败:",e),m.error("选择模板失败: "+(e.message||"未知错误"))}}async function yt(t){try{await H.confirm("确定要删除这个模板吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),await wt(t)}catch(e){if(e==="cancel")return;console.error("确认删除模板失败:",e)}}async function wt(t){try{if(t==="scene_default"){m.warning("无法删除默认模板");return}t===L.value&&m.info("删除当前使用的模板后将切换到默认模板");const n=await window.pywebview.api.book_controller.delete_bridge_config(null,t),i=typeof n=="string"?JSON.parse(n):n;if(i.success||i.status==="success"){if(O.value=O.value.filter(a=>a.id!==t),t===L.value){const a=O.value.find(c=>c.id==="scene_default")||O.value[0];a?xe(a.id):Pe()}m.success("模板已删除")}else throw new Error(i.message||"删除失败")}catch(e){console.error("删除配置失败:",e),m.error("删除配置失败: "+(e.message||"未知错误"))}}async function ze(t){if(t)try{if(S.value=!0,q.value&&!await H.confirm("当前设计有未保存的更改，切换将丢失这些更改。是否继续？","未保存的更改",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).catch(()=>!1)){S.value=!1;return}const e=await window.pywebview.api.book_controller.load_scene_design(t);if(!e.success){m.error(e.message||"加载设计失败"),S.value=!1;return}const n=e.data,i={...n,bridgeA:n.bridgeA||n.sceneA||{},bridgeB:n.bridgeB||n.sceneB||{}};Object.assign(o,i),L.value=i.id,Oe.value=i.id,q.value=!1,m.success(`已加载设计: ${i.name||i.id}`),console.log("加载历史设计成功:",i)}catch(e){console.error("加载历史设计失败",e),m.error("加载历史设计失败: "+(e.message||"未知错误"))}finally{S.value=!1}}ll(async()=>{try{let t=await window.pywebview.api.book_controller.get_bridge_configs();if(typeof t=="string")try{t=JSON.parse(t)}catch(e){console.error("解析后端响应失败:",e)}if((t.success||t.status==="success")&&t.data&&t.data.length>0){O.value=t.data;const e=t.data[0],n={...e,bridgeA:e.bridgeA||e.sceneA||{},bridgeB:e.bridgeB||e.sceneB||{}};Object.assign(o,n),L.value=n.id,console.log("加载配置成功:",o)}else console.log("未检测到现有配置，使用默认配置"),m.info("未检测到现有配置，使用默认配置");if(await Ne(),G.value.length>0){const e=G.value[0];Oe.value=e.id,await ze(e.id)}else await Lt()}catch(t){console.error("加载配置失败",t),m.warning("加载配置失败，使用默认配置")}Bt(),document.addEventListener("keydown",Re),window.addEventListener("beforeunload",et)}),nt(()=>{K.value&&clearTimeout(K.value),document.removeEventListener("keydown",Re),window.removeEventListener("beforeunload",et)});function Re(t){(t.ctrlKey||t.metaKey)&&t.key==="s"&&(t.preventDefault(),de(!0))}async function re(){try{if(S.value=!0,o.isDesign||o.id&&o.id.startsWith("design_"))return console.log("当前是设计而非配置模板，跳过保存到配置中"),await de(!0);const t=JSON.parse(JSON.stringify(o));t.sceneA=t.bridgeA||{},t.sceneB=t.bridgeB||{};const e=t.id||null;console.log("准备保存配置:",e,t);let n=await window.pywebview.api.book_controller.save_bridge_config(e,t);if(typeof n=="string")try{n=JSON.parse(n)}catch(i){throw console.error("解析后端响应失败:",i),new Error("后端响应格式不正确")}if(n.success||n.status==="success"){const i=n.data||{},a=O.value.findIndex(c=>c.id===(i.id||e));return a!==-1?O.value[a]={...i}:O.value.push({...i}),i.id&&(L.value=i.id,o.id=i.id),i.sceneA&&(o.sceneA=i.sceneA,o.bridgeA=i.sceneA),i.sceneB&&(o.sceneB=i.sceneB,o.bridgeB=i.sceneB),q.value=!1,console.log("配置保存成功:",i),!0}else return console.error("保存配置失败:",n),m.error(`保存失败: ${n.message||"未知错误"}`),!1}catch(t){return console.error("保存配置失败:",t),m.error(`保存失败: ${t.message||"未知错误"}`),S.value=!1,!1}finally{S.value=!1}}async function Ct(t){try{let e;if(t&&t!==o.id){const i=O.value.find(a=>a.id===t);if(!i)throw new Error("未找到指定的配置");e={...i}}else await re(),e={...o};const n=JSON.stringify(e,null,2);await window.pywebview.api.copy_to_clipboard(n),m({message:`配置 "${e.name}" 已复制到剪贴板`,type:"success",duration:2e3})}catch(e){console.error("导出失败:",e),m.error("导出失败: "+(e.message||"复制到剪贴板失败，请检查浏览器权限"))}}function At(){be.value="",Q.value="",ge.value=!0}async function Vt(){Q.value="",ae.value=!0;try{if(!be.value.trim()){Q.value="请输入JSON配置",ae.value=!1;return}const t=JSON.parse(be.value);if(!t.bridgeA&&!t.sceneA&&!t.bridgeB&&!t.sceneB){Q.value="导入的数据格式不正确，缺少必要的剧情信息",ae.value=!1;return}const e={...t,bridgeA:t.bridgeA||t.sceneA||{},bridgeB:t.bridgeB||t.sceneB||{},sceneA:t.sceneA||t.bridgeA||{},sceneB:t.sceneB||t.bridgeB||{}};Object.assign(o,e),o.id||(o.id="scene_"+Date.now()),await re()?(ge.value=!1,m.success("配置导入成功")):Q.value="保存导入的配置失败，请检查数据格式",ae.value=!1}catch(t){console.error("导入失败:",t),Q.value=t.message||"无法解析JSON数据",ae.value=!1}}function Bt(){o.sceneA&&!o.bridgeA&&(o.bridgeA=o.sceneA),o.sceneB&&!o.bridgeB&&(o.bridgeB=o.sceneB),o.bridgeA||(o.bridgeA={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]}),o.bridgeB||(o.bridgeB={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]}),o.guidelines||(o.guidelines=[])}function qe(t,e){Array.isArray(o[t][e])&&o[t][e].push("")}function Ge(t,e,n){Array.isArray(o[t][e])&&o[t][e].splice(n,1)}function He(t,e){if(Array.isArray(o[t][e])&&o[t][e].length>0){const n=o[t][e][0],i={};for(const a in n)i[a]="";o[t][e].push(i)}}function Qe(t,e,n){Array.isArray(o[t][e])&&(o[t][e].splice(n,1),o[t][e].length===0&&e==="读者感受/效果"&&o[t][e].push({效果:"",手段:""}))}function Ye(t,e){typeof o[t][e]=="object"&&o[t][e]!==null&&!Array.isArray(o[t][e])&&H.prompt("请输入属性名","添加属性",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S+/,inputErrorMessage:"属性名不能为空"}).then(({value:n})=>{if(n in o[t][e]){m.warning("属性名已存在");return}o[t][e][n]="",m({type:"success",message:"属性添加成功"})}).catch(()=>{})}function We(t,e,n){typeof o[t][e]=="object"&&o[t][e]!==null&&!Array.isArray(o[t][e])&&H.confirm(`确定要删除属性 "${n}" 吗？`,"删除确认",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{delete o[t][e][n],Object.keys(o[t][e]).length===0&&(o[t][e].默认=""),m({type:"success",message:"属性已删除"})}).catch(()=>{})}function jt(){H.confirm("确定要交换A和B剧情的数据吗？此操作将互换两侧的所有内容。","交换剧情数据",{confirmButtonText:"确认交换",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=JSON.parse(JSON.stringify(o.bridgeA)),e=JSON.parse(JSON.stringify(o.bridgeB));o.bridgeA=e,o.bridgeB=t,re(),m({type:"success",message:"A/B剧情数据交换成功",duration:2e3})}).catch(()=>{m({type:"info",message:"已取消交换操作",duration:1500})})}function Xe(t,e=null){let n=e;!n&&t&&(n=O.value.find(i=>i.id===t)),n||(n=o),D.id=n.id,D.name=n.name,D.description=n.description||"",e?(D.bridgeA=e.bridgeA||{},D.bridgeB=e.bridgeB||{}):(D.bridgeA={},D.bridgeB={}),$t(n),X.value=!0}function $t(t){Object.keys(u).forEach(i=>{delete u[i]});const e=Object.keys(t.bridgeA||{}).length>0?t.bridgeA:t.bridgeB;let n=1;Object.keys(e||{}).forEach(i=>{const a=e[i],c=`field_${n++}`;if(typeof a=="string")u[c]={name:i,type:"string"};else if(typeof a=="number")u[c]={name:i,type:"number"};else if(typeof a=="boolean")u[c]={name:i,type:"boolean"};else if(Array.isArray(a)){if(u[c]={name:i,type:"array",children:{}},a.length>0&&typeof a[0]=="object"){let v=1;Object.keys(a[0]||{}).forEach(C=>{const p=`child_${v++}`,h=a[0][C];u[c].children[p]={name:C,type:typeof h}})}}else if(typeof a=="object"&&a!==null){u[c]={name:i,type:"object",children:{}};let v=1;Object.keys(a).forEach(C=>{const p=`child_${v++}`,h=a[C];if(u[c].children[p]={name:C,type:typeof h=="object"&&h!==null&&!Array.isArray(h)?"object":Array.isArray(h)?"array":typeof h},typeof h=="object"&&h!==null&&!Array.isArray(h)){u[c].children[p].children={};let P=1;Object.keys(h).forEach(ee=>{const ye=`nested_${P++}`,te=h[ee];u[c].children[p].children[ye]={name:ee,type:typeof te}})}if(Array.isArray(h)&&h.length>0&&typeof h[0]=="object"){u[c].children[p].children={};let P=1;Object.keys(h[0]).forEach(ee=>{const ye=`nested_${P++}`,te=h[0][ee];u[c].children[p].children[ye]={name:ee,type:typeof te}})}})}})}function Ze(){const t=`field_${Date.now()}`;u[t]={name:"新字段",type:"string"}}function Ot(t,e){u[t]&&(u[t].name=e)}function kt(t,e){if(u[t]){const n=u[t],i=n.type;if(n.type=e,e==="object"||e==="array"){if(n.children||(n.children={}),Object.keys(n.children).length===0){const a=`child_${Date.now()}`;n.children[a]={name:e==="object"?"默认字段":"项目",type:"string"},m({message:`已自动添加一个默认${e==="object"?"字段":"项目"}，您可以根据需要修改`,type:"info",duration:3e3})}}else i==="object"||i==="array"?H.confirm("切换为简单类型将丢失所有子字段数据，确定要继续吗？","类型转换警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{delete n.children}).catch(()=>{n.type=i}):delete n.children}}function Dt(t){H.confirm("确定删除此字段吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{delete u[t],m({message:"字段已删除",type:"success",duration:1500})})}function Ie(t){if(u[t]&&(u[t].type==="object"||u[t].type==="array")){u[t].children||(u[t].children={});const e=`child_${Date.now()}`;u[t].children[e]={name:"子字段",type:"string"}}}function Et(t,e,n){u[t]&&u[t].children&&u[t].children[e]&&(u[t].children[e].name=n)}function St(t,e,n){if(u[t]&&u[t].children&&u[t].children[e]){const i=u[t].children[e];i.type=n,n==="object"||n==="array"?i.children||(i.children={}):delete i.children}}function Nt(t,e){u[t]&&u[t].children&&u[t].children[e]&&(delete u[t].children[e],m({message:"子字段已删除",type:"success",duration:1500}))}async function xt(){const t=D.name.trim();if(!t){m.warning("请输入模板名称");return}if(O.value.some(a=>a.name===t&&a.id!==D.id)){m.warning("模板名称已存在，请使用其他名称");return}const n={},i={};try{Ue(u,n,i);const a={id:D.id,name:t,description:D.description.trim(),bridgeA:n,bridgeB:i};if(a.id!==o.id){const c=await window.pywebview.api.book_controller.save_bridge_config(a.id,a),v=typeof c=="string"?JSON.parse(c):c;if(v.success||v.status==="success"){const C=O.value.findIndex(p=>p.id===a.id);C!==-1?O.value[C]={...a}:O.value.push({...a}),X.value=!1,m.success("配置保存成功")}else throw new Error(v.message||"保存失败")}else{o.name=t,o.description=D.description.trim();const c={},v={};Object.keys(n).forEach(p=>{typeof n[p]=="object"&&!Array.isArray(n[p])?(c[p]={},Object.keys(n[p]).forEach(h=>{c[p][h]=o.bridgeA&&o.bridgeA[p]&&o.bridgeA[p][h]!==void 0?o.bridgeA[p][h]:n[p][h]})):Array.isArray(n[p])?c[p]=o.bridgeA&&o.bridgeA[p]?[...o.bridgeA[p]]:[...n[p]]:c[p]=o.bridgeA&&o.bridgeA[p]!==void 0?o.bridgeA[p]:n[p]}),Object.keys(i).forEach(p=>{typeof i[p]=="object"&&!Array.isArray(i[p])?(v[p]={},Object.keys(i[p]).forEach(h=>{v[p][h]=o.bridgeB&&o.bridgeB[p]&&o.bridgeB[p][h]!==void 0?o.bridgeB[p][h]:i[p][h]})):Array.isArray(i[p])?v[p]=o.bridgeB&&o.bridgeB[p]?[...o.bridgeB[p]]:[...i[p]]:v[p]=o.bridgeB&&o.bridgeB[p]!==void 0?o.bridgeB[p]:i[p]}),o.bridgeA=c,o.bridgeB=v,await re()?(X.value=!1,m.success("配置保存成功")):m.error("保存配置失败")}}catch(a){console.error("处理配置数据失败",a),m.error("配置保存失败: "+(a.message||"配置格式错误，请检查字段设置"))}}function Ue(t,e,n){Object.entries(t).map(([a,c])=>({key:a,...c})).forEach(a=>{if(!a.name||a.name.trim()==="")return;const c=a.name.trim();if(a.type==="string")e[c]="",n[c]="";else if(a.type==="number")e[c]=0,n[c]=0;else if(a.type==="boolean")e[c]=!1,n[c]=!1;else if(a.type==="object")e[c]={},n[c]={},a.children&&Object.keys(a.children).length>0&&Ue(a.children,e[c],n[c]),Object.keys(e[c]).length===0&&(e[c].默认="",n[c].默认="");else if(a.type==="array"&&(e[c]=[],n[c]=[],a.children&&Object.keys(a.children).length>0)){const v={},C={};Ue(a.children,v,C),Object.keys(v).length>0?(e[c].push(v),n[c].push(C)):(e[c].push(""),n[c].push(""))}})}const zt=t=>{const e=o.guidelines[t];A.title=e.title||"",A.description=e.description||"",A.type=e.type||"线索铺垫",A.priority=e.priority||"normal",e.answers?Object.keys(A.answers).forEach(n=>{A.answers[n]=e.answers[n]||""}):Object.keys(A.answers).forEach(n=>{A.answers[n]=""}),oe.value=!0,Be.value=t,Z.value=!0},Ut=t=>{H.confirm("确定要删除这条铺垫指南吗？","删除确认",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{o.guidelines.splice(t,1),m.success("铺垫指南已删除")}).catch(()=>{})};function Ke(t,e){if(u[t]&&u[t].children&&u[t].children[e]){const n=u[t].children[e];if(n.type!=="object"&&n.type!=="array")return;n.children||(n.children={});const i=`nested_${Date.now()}`;n.children[i]={name:"嵌套字段",type:"string"}}}function Jt(t,e,n,i){u[t]&&u[t].children&&u[t].children[e]&&u[t].children[e].children&&u[t].children[e].children[n]&&(u[t].children[e].children[n].name=i)}function Ft(t,e,n,i){u[t]&&u[t].children&&u[t].children[e]&&u[t].children[e].children&&u[t].children[e].children[n]&&(u[t].children[e].children[n].type=i)}function Tt(t,e,n){u[t]&&u[t].children&&u[t].children[e]&&u[t].children[e].children&&u[t].children[e].children[n]&&(delete u[t].children[e].children[n],m({message:"嵌套子字段已删除",type:"success",duration:1500}))}async function Lt(){try{S.value=!0;let t=await window.pywebview.api.book_controller.load_scene_design(o.id);if(typeof t=="string")try{t=JSON.parse(t)}catch(e){console.error("解析后端响应失败:",e),S.value=!1;return}t.success&&t.data&&(t.data.bridgeA?o.bridgeA=t.data.bridgeA:t.data.sceneA&&(o.bridgeA=t.data.sceneA),t.data.bridgeB?o.bridgeB=t.data.bridgeB:t.data.sceneB&&(o.bridgeB=t.data.sceneB),t.data.guidelines&&(o.guidelines=t.data.guidelines),m.success("设计已加载"))}catch(t){console.error("加载设计失败",t),m.error("加载设计失败: "+(t.message||"未知错误"))}finally{S.value=!1}}async function Mt(){try{let t=`# ${o.name||"剧情无痕设计"}

`;t+=`## 当前剧情 (A)

`,Object.entries(o.bridgeA).forEach(([e,n])=>{t+=`### ${e}
`,typeof n=="string"?t+=`${n||"(空)"}

`:Array.isArray(n)&&(n.length===0?t+=`(空列表)

`:typeof n[0]=="object"?n.forEach((i,a)=>{t+=`- 项目${a+1}:
`,Object.entries(i).forEach(([c,v])=>{t+=`  * ${c}: ${v||"(空)"}
`}),t+=`
`}):(n.forEach(i=>{t+=`- ${i||"(空项)"}
`}),t+=`
`))}),t+=`## 目标剧情 (B)

`,Object.entries(o.bridgeB).forEach(([e,n])=>{t+=`### ${e}
`,typeof n=="string"?t+=`${n||"(空)"}

`:Array.isArray(n)&&(n.length===0?t+=`(空列表)

`:typeof n[0]=="object"?n.forEach((i,a)=>{t+=`- 项目${a+1}:
`,Object.entries(i).forEach(([c,v])=>{t+=`  * ${c}: ${v||"(空)"}
`}),t+=`
`}):(n.forEach(i=>{t+=`- ${i||"(空项)"}
`}),t+=`
`))}),t+=`## 无痕铺垫

`,o.guidelines&&o.guidelines.length>0?o.guidelines.forEach((e,n)=>{t+=`### 铺垫 ${n+1}: ${e.title||"无标题"}
`,t+=`- 类型: ${e.type||"未分类"}
`,t+=`- 优先级: ${e.priority==="high"?"高":"普通"}
`,t+=`- 描述: ${e.description||"(无描述)"}

`}):t+=`(暂无铺垫)

`,await window.pywebview.api.copy_to_clipboard(t),m.success("内容已复制到剪贴板")}catch(t){console.error("复制内容失败",t),m.error("复制到剪贴板失败，请检查浏览器权限")}}async function de(t=!1){if(!o.name&&t){z.name="",z.description=o.description||"",se.value=!0;return}if(!t){z.name=o.name||"",z.description=o.description||"",se.value=!0;return}try{S.value=!0,o.timestamp||(o.timestamp=Date.now()),(!o.id||o.id==="scene_default")&&(o.id="design_"+Date.now());const e={id:o.id,name:o.name||`设计_${new Date().toLocaleDateString()}`,description:o.description||"",bridgeA:o.bridgeA,bridgeB:o.bridgeB,guidelines:o.guidelines,timestamp:Date.now()},n=await window.pywebview.api.book_controller.save_scene_design(e);if(!n.success)throw new Error(n.message||"调用后端API保存设计失败");L.value=e.id,Oe.value=e.id,await Ne(),q.value=!1,m.success("设计已保存")}catch(e){console.error("保存设计失败",e),m.error("保存失败: "+(e.message||"未知错误"))}finally{S.value=!1}}function et(t){if(q.value){const e="您有未保存的更改，确定离开吗？";return t.returnValue=e,e}}function Pt(t){const e=Object.keys(u),n=e.indexOf(t);if(n>0){const i=e[n-1],a={};e.forEach((c,v)=>{v===n-1?a[c]={...u[t]}:v===n?a[c]={...u[i]}:a[c]=u[c]}),Object.keys(u).forEach(c=>{delete u[c]}),Object.keys(a).forEach(c=>{u[c]=a[c]}),m.success("字段已上移")}}function Rt(t){const e=Object.keys(u),n=e.indexOf(t);if(n<e.length-1){const i=e[n+1],a={};e.forEach((c,v)=>{v===n?a[c]={...u[i]}:v===n+1?a[c]={...u[t]}:a[c]=u[c]}),Object.keys(u).forEach(c=>{delete u[c]}),Object.keys(a).forEach(c=>{u[c]=a[c]}),m.success("字段已下移")}}function qt(t,e){const n=u[t];if(!n||!n.children)return;const i=Object.keys(n.children),a=i.indexOf(e);if(a>0){const c=i[a-1],v={};i.forEach((C,p)=>{p===a-1?v[C]={...n.children[e]}:p===a?v[C]={...n.children[c]}:v[C]=n.children[C]}),Object.keys(n.children).forEach(C=>{delete n.children[C]}),Object.keys(v).forEach(C=>{n.children[C]=v[C]}),m.success("子字段已上移")}}function Gt(t,e){const n=u[t];if(!n||!n.children)return;const i=Object.keys(n.children),a=i.indexOf(e);if(a<i.length-1){const c=i[a+1],v={};i.forEach((C,p)=>{p===a?v[C]={...n.children[c]}:p===a+1?v[C]={...n.children[e]}:v[C]=n.children[C]}),Object.keys(n.children).forEach(C=>{delete n.children[C]}),Object.keys(v).forEach(C=>{n.children[C]=v[C]}),m.success("子字段已下移")}}function Ht(t,e,n){const i=u[t];if(!i||!i.children)return;const a=i.children[e];if(!a||!a.children)return;const c=Object.keys(a.children),v=c.indexOf(n);if(v>0){const C=c[v-1],p={};c.forEach((h,P)=>{P===v-1?p[h]={...a.children[n]}:P===v?p[h]={...a.children[C]}:p[h]=a.children[h]}),Object.keys(a.children).forEach(h=>{delete a.children[h]}),Object.keys(p).forEach(h=>{a.children[h]=p[h]}),m.success("嵌套字段已上移")}}function Qt(t,e,n){const i=u[t];if(!i||!i.children)return;const a=i.children[e];if(!a||!a.children)return;const c=Object.keys(a.children),v=c.indexOf(n);if(v<c.length-1){const C=c[v+1],p={};c.forEach((h,P)=>{P===v?p[h]={...a.children[C]}:P===v+1?p[h]={...a.children[n]}:p[h]=a.children[h]}),Object.keys(a.children).forEach(h=>{delete a.children[h]}),Object.keys(p).forEach(h=>{a.children[h]=p[h]}),m.success("嵌套字段已下移")}}function Yt(t){switch(t){case"copyJson":Wt();break;case"importDesign":Xt();break}}async function Wt(){try{const t={id:o.id,name:o.name,description:o.description||"",bridgeA:o.bridgeA,bridgeB:o.bridgeB,guidelines:o.guidelines,timestamp:Date.now()},e=JSON.stringify(t,null,2);await window.pywebview.api.copy_to_clipboard(e),m({message:"设计JSON已复制到剪贴板",type:"success",duration:2e3})}catch(t){console.error("复制设计JSON失败",t),m.error("复制到剪贴板失败，请检查浏览器权限")}}function Xt(){_e.value="",I.value="",ve.value=!0}async function Zt(){I.value="",he.value=!0;try{if(!_e.value.trim()){I.value="请输入JSON设计数据",he.value=!1;return}const t=JSON.parse(_e.value);if(!t.bridgeA&&!t.sceneA&&!t.bridgeB&&!t.sceneB){I.value="导入的数据格式不正确，缺少必要的剧情信息",he.value=!1;return}const e={...t,id:"design_"+Date.now(),timestamp:Date.now(),bridgeA:t.bridgeA||t.sceneA||{},bridgeB:t.bridgeB||t.sceneB||{},guidelines:t.guidelines||[]};Object.assign(o,e),L.value=e.id,await de(!0),ve.value=!1,m.success("设计导入成功并已保存")}catch(t){console.error("导入设计失败:",t),I.value=t.message||"无法解析JSON数据"}finally{he.value=!1}}return(t,e)=>{const n=ol,i=sl,a=al,c=rl,v=cl,C=ul,p=nl("Switch"),h=gl,P=ml,ee=vl,ye=wl,te=Cl,ce=Al,R=$l,je=jl,E=kl,we=Ol,tt=Bl,Ce=Vl,It=Dl,Kt=hl;return b(),_("div",El,[d("div",Sl,[d("div",Nl,[d("div",xl,[e[32]||(e[32]=d("h2",null,"剧情无痕设计",-1)),l(C,{placement:"bottom-start",width:380,trigger:"click","popper-class":"history-design-popover"},{reference:s(()=>[d("span",zl,[w(j(o.name||"未命名设计")+" ",1),l(n,{class:"selector-icon"},{default:s(()=>[l(y(pe))]),_:1})])]),default:s(()=>[d("div",Ul,[d("div",Jl,[e[31]||(e[31]=d("span",{class:"panel-title"},"历史设计",-1)),l(i,{type:"primary",size:"small",plain:"",onClick:Me},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[30]||(e[30]=w(" 新建设计 "))]),_:1})]),d("div",Fl,[l(a,{modelValue:Ve.value,"onUpdate:modelValue":e[0]||(e[0]=r=>Ve.value=r),placeholder:"搜索设计...","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),d("div",Tl,[Le.value.length===0?(b(),_("div",Ll,[l(c,{description:"暂无历史设计","image-size":60})])):(b(),_("div",Ml,[(b(!0),_(N,null,x(Le.value,r=>(b(),_("div",{key:r.id,class:le(["history-item",{active:r.id===L.value}]),onClick:f=>ze(r.id)},[d("div",Rl,[d("div",ql,j(r.name||"未命名设计"),1),d("div",Gl,[d("div",Hl,[l(n,null,{default:s(()=>[l(y(dl))]),_:1}),d("span",null,j(ft(r.timestamp)),1)]),d("div",Ql,[l(v,{size:"small",effect:"plain"},{default:s(()=>[w(j(r.guidelines?.length||0)+" 个铺垫 ",1)]),_:2},1024)])])]),d("div",Yl,[l(i,{type:"danger",size:"small",circle:"",plain:"",onClick:ne(f=>gt(r.id),["stop"])},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])])],10,Pl))),128))]))])])]),_:1})]),d("div",Wl,[l(i,{type:"primary",onClick:Me,size:"default",class:"action-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[33]||(e[33]=w(" 新建 "))]),_:1}),l(i,{type:"primary",onClick:e[1]||(e[1]=r=>de(!0)),size:"default",class:"action-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(it))]),_:1}),e[34]||(e[34]=w(" 保存 "))]),_:1}),l(i,{type:"warning",onClick:jt,size:"default",class:"action-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(p)]),_:1}),e[35]||(e[35]=w(" 交换剧情 "))]),_:1}),l(i,{type:"success",onClick:Mt,size:"default",class:"action-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(fl))]),_:1}),e[36]||(e[36]=w(" 复制 "))]),_:1}),l(ee,{trigger:"click",onCommand:Yt},{dropdown:s(()=>[l(P,null,{default:s(()=>[l(h,{command:"copyJson"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(st))]),_:1}),e[38]||(e[38]=w(" 复制设计JSON "))]),_:1}),l(h,{command:"importDesign"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(bl))]),_:1}),e[39]||(e[39]=w(" 导入设计JSON "))]),_:1})]),_:1})]),default:s(()=>[l(i,{type:"info",size:"default",class:"action-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(pl))]),_:1}),e[37]||(e[37]=w(" 更多 ")),l(n,{class:"el-icon--right"},{default:s(()=>[l(y(pe))]),_:1})]),_:1})]),_:1}),l(C,{placement:"bottom-start",width:380,trigger:"click","popper-class":"config-manager-popover"},{reference:s(()=>[l(i,{type:"info",size:"default",class:"action-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(_l))]),_:1}),e[40]||(e[40]=w(" 模板 ")),l(n,{class:"el-icon--right"},{default:s(()=>[l(y(pe))]),_:1})]),_:1})]),default:s(()=>[d("div",Xl,[d("div",Zl,[e[43]||(e[43]=d("span",{class:"panel-title"},"模板管理",-1)),d("div",Il,[l(i,{type:"primary",size:"small",plain:"",onClick:vt},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[41]||(e[41]=w(" 新建模板 "))]),_:1}),l(i,{type:"success",size:"small",plain:"",onClick:At},{default:s(()=>[l(n,null,{default:s(()=>[l(y(st))]),_:1}),e[42]||(e[42]=w(" 导入模板 "))]),_:1})])]),d("div",Kl,[l(a,{modelValue:Ae.value,"onUpdate:modelValue":e[2]||(e[2]=r=>Ae.value=r),placeholder:"搜索模板...","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),d("div",en,[Te.value.length===0?(b(),_("div",tn,[l(c,{description:"暂无模板","image-size":60})])):(b(),_("div",ln,[(b(!0),_(N,null,x(Te.value,r=>(b(),_("div",{key:r.id,class:le(["config-item",{active:o.id===r.id}]),onClick:f=>_t(r.id)},[d("div",sn,[d("div",on,j(r.name||"未命名模板"),1),d("div",an,[d("div",rn,j(r.description||"无描述"),1),d("div",dn,[l(v,{size:"small",effect:"plain"},{default:s(()=>[w(j(Object.keys(r.bridgeA||{}).length)+" 个字段 ",1)]),_:2},1024)])])]),d("div",cn,[l(i,{type:"success",size:"small",circle:"",plain:"",onClick:ne(f=>ht(r.id),["stop"]),title:"基于此模板创建新设计"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1})]),_:2},1032,["onClick"]),l(i,{type:"primary",size:"small",circle:"",plain:"",onClick:ne(f=>Xe(r.id),["stop"]),title:"编辑模板"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(Fe))]),_:1})]),_:2},1032,["onClick"]),l(i,{type:"info",size:"small",circle:"",plain:"",onClick:ne(f=>Ct(r.id),["stop"]),title:"导出模板"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(it))]),_:1})]),_:2},1032,["onClick"]),l(i,{type:"danger",size:"small",circle:"",plain:"",onClick:ne(f=>yt(r.id),["stop"]),title:"删除模板"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])])],10,nn))),128))]))])])]),_:1}),q.value?(b(),W(i,{key:0,type:"danger",size:"default",plain:"",class:"unsaved-indicator"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(Fe))]),_:1}),e[44]||(e[44]=w(" 未保存 "))]),_:1})):T("",!0)])])]),il((b(),_("div",un,[d("div",fn,[d("div",pn,[e[48]||(e[48]=d("div",{class:"panel-header"},[d("h3",null,"当前剧情 (A)")],-1)),d("div",mn,[d("div",gn,[(b(!0),_(N,null,x(o.bridgeA,(r,f)=>(b(),_("div",{key:`bridgeA-${f}`,class:"json-field"},[d("div",bn,j(f),1),d("div",vn,[typeof r=="string"?(b(),W(a,{key:0,modelValue:o.bridgeA[f],"onUpdate:modelValue":$=>o.bridgeA[f]=$,type:"textarea",rows:2,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])):Array.isArray(r)&&(r.length===0||typeof r[0]!="object")?(b(),_("div",_n,[(b(!0),_(N,null,x(r,($,g)=>(b(),_("div",{key:`bridgeA-${f}-${g}`,class:"array-item"},[l(a,{rows:3,type:"textarea",modelValue:o.bridgeA[f][g],"onUpdate:modelValue":V=>o.bridgeA[f][g]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"]),l(i,{onClick:V=>Ge("bridgeA",f,g),circle:"",size:"small"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])]))),128)),l(i,{onClick:$=>qe("bridgeA",f),class:"add-item-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[45]||(e[45]=w(" 添加项目 "))]),_:2},1032,["onClick"])])):Array.isArray(r)&&typeof r[0]=="object"?(b(),_("div",hn,[(b(!0),_(N,null,x(r,($,g)=>(b(),_("div",{key:`bridgeA-${f}-obj-${g}`,class:"object-item"},[d("div",yn,[d("span",null,"项目 "+j(g+1),1),l(i,{onClick:V=>Qe("bridgeA",f,g),circle:"",size:"small"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])]),(b(!0),_(N,null,x($,(V,F)=>(b(),_("div",{key:`bridgeA-${f}-obj-${g}-${F}`,class:"object-prop"},[d("span",wn,j(F)+":",1),l(a,{rows:3,type:"textarea",modelValue:o.bridgeA[f][g][F],"onUpdate:modelValue":k=>o.bridgeA[f][g][F]=k,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128))]))),128)),l(i,{onClick:$=>He("bridgeA",f),class:"add-item-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[46]||(e[46]=w(" 添加项目 "))]),_:2},1032,["onClick"])])):typeof r=="object"&&r!==null&&!Array.isArray(r)?(b(),_("div",Cn,[(b(!0),_(N,null,x(r,($,g)=>(b(),_("div",{key:`bridgeA-${f}-prop-${g}`,class:"object-prop"},[d("div",An,[d("span",Vn,j(g)+":",1),l(i,{onClick:V=>We("bridgeA",f,g),circle:"",size:"small"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])]),l(a,{rows:3,type:"textarea",modelValue:o.bridgeA[f][g],"onUpdate:modelValue":V=>o.bridgeA[f][g]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128)),l(i,{onClick:$=>Ye("bridgeA",f),class:"add-item-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[47]||(e[47]=w(" 添加属性 "))]),_:2},1032,["onClick"])])):T("",!0)])]))),128))])])]),d("div",Bn,[e[52]||(e[52]=d("div",{class:"panel-header"},[d("h3",null,"目标剧情 (B)")],-1)),d("div",jn,[d("div",$n,[(b(!0),_(N,null,x(o.bridgeB,(r,f)=>(b(),_("div",{key:`bridgeB-${f}`,class:"json-field"},[d("div",On,j(f),1),d("div",kn,[typeof r=="string"?(b(),W(a,{key:0,modelValue:o.bridgeB[f],"onUpdate:modelValue":$=>o.bridgeB[f]=$,type:"textarea",rows:2,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])):Array.isArray(r)&&(r.length===0||typeof r[0]!="object")?(b(),_("div",Dn,[(b(!0),_(N,null,x(r,($,g)=>(b(),_("div",{key:`bridgeB-${f}-${g}`,class:"array-item"},[l(a,{rows:3,type:"textarea",modelValue:o.bridgeB[f][g],"onUpdate:modelValue":V=>o.bridgeB[f][g]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"]),l(i,{onClick:V=>Ge("bridgeB",f,g),circle:"",size:"small"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])]))),128)),l(i,{onClick:$=>qe("bridgeB",f),class:"add-item-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[49]||(e[49]=w(" 添加项目 "))]),_:2},1032,["onClick"])])):Array.isArray(r)&&typeof r[0]=="object"?(b(),_("div",En,[(b(!0),_(N,null,x(r,($,g)=>(b(),_("div",{key:`bridgeB-${f}-obj-${g}`,class:"object-item"},[d("div",Sn,[d("span",null,"项目 "+j(g+1),1),l(i,{onClick:V=>Qe("bridgeB",f,g),circle:"",size:"small"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])]),(b(!0),_(N,null,x($,(V,F)=>(b(),_("div",{key:`bridgeB-${f}-obj-${g}-${F}`,class:"object-prop"},[d("span",Nn,j(F)+":",1),l(a,{rows:3,type:"textarea",modelValue:o.bridgeB[f][g][F],"onUpdate:modelValue":k=>o.bridgeB[f][g][F]=k,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128))]))),128)),l(i,{onClick:$=>He("bridgeB",f),class:"add-item-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[50]||(e[50]=w(" 添加项目 "))]),_:2},1032,["onClick"])])):typeof r=="object"&&r!==null&&!Array.isArray(r)?(b(),_("div",xn,[(b(!0),_(N,null,x(r,($,g)=>(b(),_("div",{key:`bridgeB-${f}-prop-${g}`,class:"object-prop"},[d("div",zn,[d("span",Un,j(g)+":",1),l(i,{onClick:V=>We("bridgeB",f,g),circle:"",size:"small"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])]),l(a,{rows:3,type:"textarea",modelValue:o.bridgeB[f][g],"onUpdate:modelValue":V=>o.bridgeB[f][g]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128)),l(i,{onClick:$=>Ye("bridgeB",f),class:"add-item-btn"},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[51]||(e[51]=w(" 添加属性 "))]),_:2},1032,["onClick"])])):T("",!0)])]))),128))])])])]),d("div",{class:"drawer-toggle",onClick:rt},[l(n,{class:le({"is-rotate":me.value})},{default:s(()=>[l(y($e))]),_:1},8,["class"]),d("span",null,j(me.value?"收起铺垫":"展开铺垫"),1)]),d("div",{class:le(["drawer-panel",{"drawer-visible":me.value}])},[d("div",Jn,[d("div",Fn,[d("div",Tn,[e[53]||(e[53]=d("div",{class:"tech-lines"},null,-1)),e[54]||(e[54]=d("h3",null,"无痕铺垫",-1)),l(ye,{content:"设计从A到B剧情的铺垫要素"},{default:s(()=>[l(n,{class:"guide-icon"},{default:s(()=>[l(y(yl))]),_:1})]),_:1})]),l(i,{type:"primary",size:"small",class:"add-guideline-btn",onClick:ct},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[55]||(e[55]=w(" 添加铺垫指南 "))]),_:1})]),d("div",Ln,[(b(!0),_(N,null,x(o.guidelines,(r,f)=>(b(),_("div",{key:f,class:le(["guideline-item",{"high-priority":r.priority==="high"}])},[d("div",Mn,[d("span",Pn,j(r.title),1),l(v,{type:r.priority==="high"?"danger":r.priority==="medium"?"warning":"info",size:"small"},{default:s(()=>[w(j(r.priority==="high"?"高优先级":r.priority==="medium"?"中优先级":"低优先级"),1)]),_:2},1032,["type"])]),d("div",Rn,j(r.description),1),d("div",qn,[l(i,{type:"primary",size:"small",text:"",onClick:$=>zt(f)},{default:s(()=>[l(n,null,{default:s(()=>[l(y(Fe))]),_:1}),e[56]||(e[56]=w(" 编辑 "))]),_:2},1032,["onClick"]),l(i,{type:"danger",size:"small",text:"",onClick:$=>Ut(f)},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1}),e[57]||(e[57]=w(" 删除 "))]),_:2},1032,["onClick"])])],2))),128)),!o.guidelines||o.guidelines.length===0?(b(),W(c,{key:0,description:"暂无铺垫指南，点击添加按钮创建","image-size":120})):T("",!0)])])],2)])),[[Kt,S.value]]),l(ce,{modelValue:ge.value,"onUpdate:modelValue":e[5]||(e[5]=r=>ge.value=r),title:"导入剧情配置",width:"600px","lock-scroll":!0,"destroy-on-close":!1},{footer:s(()=>[d("div",Qn,[l(i,{onClick:e[4]||(e[4]=r=>ge.value=!1),plain:""},{default:s(()=>e[60]||(e[60]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:Vt,loading:ae.value},{default:s(()=>e[61]||(e[61]=[w(" 导入 ")])),_:1},8,["loading"])])]),default:s(()=>[d("div",Gn,[e[58]||(e[58]=d("p",{class:"import-tip"},"请将剧情配置的JSON字符串粘贴到下方文本框中:",-1)),l(a,{modelValue:be.value,"onUpdate:modelValue":e[3]||(e[3]=r=>be.value=r),type:"textarea",rows:10,placeholder:"粘贴JSON配置文本...",resize:"none"},null,8,["modelValue"]),e[59]||(e[59]=d("div",{class:"import-help"},[d("p",null,[d("i",{class:"el-icon-info"}),w(" 提示：导入会替换当前所有配置内容")])],-1)),Q.value?(b(),_("div",Hn,[l(te,{title:Q.value,type:"error","show-icon":"",closable:!1},null,8,["title"])])):T("",!0)])]),_:1},8,["modelValue"]),l(ce,{modelValue:Z.value,"onUpdate:modelValue":e[12]||(e[12]=r=>Z.value=r),title:oe.value?"编辑无痕铺垫":"添加无痕铺垫",width:"800px",top:"5vh",class:"guideline-dialog","modal-append-to-body":!0,"destroy-on-close":"","close-on-click-modal":!1,"show-close":!1,fullscreen:""},{header:s(({titleId:r,titleClass:f})=>[d("div",Yn,[d("h4",{id:r,class:le(f)},j(oe.value?"编辑无痕铺垫":"添加无痕铺垫"),11,Wn),l(i,{class:"dialog-close-btn",onClick:e[6]||(e[6]=$=>Z.value=!1),icon:"Close"})])]),footer:s(()=>[d("div",oi,[l(i,{onClick:e[11]||(e[11]=r=>Z.value=!1)},{default:s(()=>e[65]||(e[65]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:ut},{default:s(()=>[w(j(oe.value?"更新":"保存"),1)]),_:1})])]),default:s(()=>[d("div",Xn,[d("div",Zn,[l(Ce,{model:A,"label-position":"top"},{default:s(()=>[l(tt,{gutter:20},{default:s(()=>[l(je,{span:16},{default:s(()=>[l(R,{label:"铺垫标题"},{default:s(()=>[l(a,{modelValue:A.title,"onUpdate:modelValue":e[7]||(e[7]=r=>A.title=r),placeholder:"为铺垫添加简短标题"},null,8,["modelValue"])]),_:1})]),_:1}),l(je,{span:8},{default:s(()=>[l(R,{label:"铺垫类型"},{default:s(()=>[l(we,{modelValue:A.type,"onUpdate:modelValue":e[8]||(e[8]=r=>A.type=r),placeholder:"选择铺垫类型",class:"full-width"},{default:s(()=>[l(E,{label:"线索铺垫",value:"线索铺垫"}),l(E,{label:"人物特征",value:"人物特征"}),l(E,{label:"情感基调",value:"情感基调"}),l(E,{label:"视角处理",value:"视角处理"}),l(E,{label:"细节设计",value:"细节设计"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),d("div",In,[d("div",Kn,[e[62]||(e[62]=d("h3",{class:"section-title"},"铺垫设计思考",-1)),d("div",ei,[(b(),_(N,null,x(dt,r=>d("div",{key:r.key,class:"dimension-row"},[d("div",ti,[d("div",li,j(r.dimension),1),d("div",ni,j(r.question),1)]),d("div",ii,[l(a,{type:"textarea",rows:2,modelValue:A.answers[r.key],"onUpdate:modelValue":f=>A.answers[r.key]=f,placeholder:r.placeholder},null,8,["modelValue","onUpdate:modelValue","placeholder"])])])),64))])]),d("div",si,[e[64]||(e[64]=d("h3",{class:"section-title"},"完整描述",-1)),l(Ce,{model:A,"label-position":"top"},{default:s(()=>[l(R,{label:""},{default:s(()=>[l(a,{modelValue:A.description,"onUpdate:modelValue":e[9]||(e[9]=r=>A.description=r),type:"textarea",rows:3,placeholder:"根据上面的思考，总结完整的铺垫描述"},null,8,["modelValue"])]),_:1}),l(R,{class:"priority-checkbox"},{default:s(()=>[l(It,{modelValue:A.priority,"onUpdate:modelValue":e[10]||(e[10]=r=>A.priority=r),"true-label":"high","false-label":"normal"},{default:s(()=>e[63]||(e[63]=[d("span",{class:"priority-label"},"标记为高优先级",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])])])]),_:1},8,["modelValue","title"]),l(ce,{modelValue:ie.value,"onUpdate:modelValue":e[16]||(e[16]=r=>ie.value=r),title:"基于模板创建新设计",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onOpened:pt},{footer:s(()=>[d("span",ri,[l(i,{onClick:e[15]||(e[15]=r=>ie.value=!1)},{default:s(()=>e[67]||(e[67]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:Ee,disabled:!J.name},{default:s(()=>e[68]||(e[68]=[w(" 创建设计 ")])),_:1},8,["disabled"])])]),default:s(()=>[d("div",ai,[l(te,{type:"info",closable:!1,"show-icon":"",class:"theme-adaptive-alert"},{default:s(()=>e[66]||(e[66]=[d("p",null,"您正在基于模板创建新的设计。这将不会修改模板本身，而是创建一个独立的设计保存到您的设计列表中。",-1)])),_:1})]),l(Ce,{model:J,"label-position":"top",onSubmit:ne(Ee,["prevent"])},{default:s(()=>[l(R,{label:"设计名称",required:""},{default:s(()=>[l(a,{ref_key:"designNameInputRef",ref:ke,modelValue:J.name,"onUpdate:modelValue":e[13]||(e[13]=r=>J.name=r),placeholder:"请输入设计名称",maxlength:"50","show-word-limit":"",onKeyup:ot(Ee,["enter"])},null,8,["modelValue"])]),_:1}),l(R,{label:"选择配置模板"},{default:s(()=>[l(we,{modelValue:J.configId,"onUpdate:modelValue":e[14]||(e[14]=r=>J.configId=r),placeholder:"请选择配置模板",class:"full-width"},{default:s(()=>[(b(!0),_(N,null,x(O.value,r=>(b(),W(E,{key:r.id,label:r.name||r.id,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(ce,{modelValue:se.value,"onUpdate:modelValue":e[20]||(e[20]=r=>se.value=r),title:"保存设计",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onOpened:mt},{footer:s(()=>[d("span",di,[l(i,{onClick:e[19]||(e[19]=r=>se.value=!1)},{default:s(()=>e[69]||(e[69]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:Se,disabled:!z.name},{default:s(()=>e[70]||(e[70]=[w(" 确认保存 ")])),_:1},8,["disabled"])])]),default:s(()=>[l(Ce,{model:z,"label-position":"top",onSubmit:ne(Se,["prevent"])},{default:s(()=>[l(R,{label:"设计名称",required:""},{default:s(()=>[l(a,{ref_key:"saveNameInputRef",ref:De,modelValue:z.name,"onUpdate:modelValue":e[17]||(e[17]=r=>z.name=r),placeholder:"请输入设计名称",maxlength:"50","show-word-limit":"",onKeyup:ot(Se,["enter"])},null,8,["modelValue"])]),_:1}),l(R,{label:"描述"},{default:s(()=>[l(a,{modelValue:z.description,"onUpdate:modelValue":e[18]||(e[18]=r=>z.description=r),type:"textarea",rows:3,placeholder:"请输入设计描述（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(ce,{modelValue:ve.value,"onUpdate:modelValue":e[23]||(e[23]=r=>ve.value=r),title:"导入设计JSON",width:"600px","close-on-click-modal":!1,"destroy-on-close":!1},{footer:s(()=>[d("div",fi,[l(i,{onClick:e[22]||(e[22]=r=>ve.value=!1),plain:""},{default:s(()=>e[73]||(e[73]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:Zt,loading:he.value},{default:s(()=>e[74]||(e[74]=[w(" 导入 ")])),_:1},8,["loading"])])]),default:s(()=>[d("div",ci,[e[71]||(e[71]=d("p",{class:"import-tip"},"请将设计的JSON字符串粘贴到下方文本框中:",-1)),l(a,{modelValue:_e.value,"onUpdate:modelValue":e[21]||(e[21]=r=>_e.value=r),type:"textarea",rows:10,placeholder:"粘贴设计JSON文本...",resize:"none"},null,8,["modelValue"]),e[72]||(e[72]=d("div",{class:"import-help"},[d("p",null,[d("i",{class:"el-icon-info"}),w(" 提示：导入会创建一个新的设计")])],-1)),I.value?(b(),_("div",ui,[l(te,{title:I.value,type:"error","show-icon":"",closable:!1},null,8,["title"])])):T("",!0)])]),_:1},8,["modelValue"]),l(ce,{modelValue:X.value,"onUpdate:modelValue":e[29]||(e[29]=r=>X.value=r),title:"编辑剧情配置",width:"800px",top:"5vh",class:"config-editor-dialog","modal-append-to-body":!0,"destroy-on-close":"","close-on-click-modal":!1,"show-close":!1,fullscreen:""},{header:s(({titleId:r,titleClass:f})=>[d("div",pi,[d("h4",{id:r,class:le(f)},"编辑剧情配置",10,mi),l(i,{class:"dialog-close-btn",onClick:e[24]||(e[24]=$=>X.value=!1),icon:"Close"})])]),footer:s(()=>[d("div",Ui,[l(i,{onClick:e[28]||(e[28]=r=>X.value=!1)},{default:s(()=>e[83]||(e[83]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:xt},{default:s(()=>e[84]||(e[84]=[w("保存配置")])),_:1})])]),default:s(()=>[d("div",gi,[d("div",bi,[l(Ce,{model:D,"label-position":"top"},{default:s(()=>[l(tt,{gutter:20},{default:s(()=>[l(je,{span:12},{default:s(()=>[l(R,{label:"配置名称"},{default:s(()=>[l(a,{modelValue:D.name,"onUpdate:modelValue":e[25]||(e[25]=r=>D.name=r),placeholder:"为配置添加名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(je,{span:12},{default:s(()=>[l(R,{label:"配置ID"},{default:s(()=>[l(a,{modelValue:D.id,"onUpdate:modelValue":e[26]||(e[26]=r=>D.id=r),placeholder:"配置唯一标识",disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(R,{label:"配置描述"},{default:s(()=>[l(a,{modelValue:D.description,"onUpdate:modelValue":e[27]||(e[27]=r=>D.description=r),type:"textarea",rows:2,placeholder:"添加配置描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),d("div",vi,[d("div",_i,[e[81]||(e[81]=d("h3",{class:"section-title"},"剧情结构定义",-1)),e[82]||(e[82]=d("p",{class:"section-description"}," 此处定义的结构模板将同时应用于A/B两个剧情，支持一层复杂的嵌套结构,暂时不更新。 ",-1)),l(i,{type:"primary",size:"small",onClick:Ze,style:{"margin-bottom":"15px"}},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1}),e[75]||(e[75]=w(" 添加根字段 "))]),_:1}),d("div",hi,[(b(!0),_(N,null,x(u,(r,f,$)=>(b(),_("div",{key:`root-${f}`,class:"field-card"},[d("div",yi,j($+1),1),d("div",wi,[d("div",Ci,[l(a,{modelValue:r.name,"onUpdate:modelValue":g=>r.name=g,placeholder:"字段名称",size:"small",onChange:g=>Ot(f,r.name)},null,8,["modelValue","onUpdate:modelValue","onChange"]),l(we,{modelValue:r.type,"onUpdate:modelValue":g=>r.type=g,placeholder:"选择类型",size:"small",onChange:g=>kt(f,r.type)},{default:s(()=>[l(E,{label:"字符串",value:"string"}),l(E,{label:"数字",value:"number"}),l(E,{label:"布尔值",value:"boolean"}),l(E,{label:"对象",value:"object"}),l(E,{label:"数组",value:"array"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),d("div",Ai,[l(i,{size:"small",type:"info",onClick:g=>Pt(f),circle:"",disabled:$===0},{default:s(()=>[l(n,null,{default:s(()=>[l(y($e))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"info",onClick:g=>Rt(f),circle:"",disabled:$===Object.keys(u).length-1},{default:s(()=>[l(n,null,{default:s(()=>[l(y(pe))]),_:1})]),_:2},1032,["onClick","disabled"]),r.type==="object"||r.type==="array"?(b(),W(i,{key:0,size:"small",type:"primary",onClick:g=>Ie(f),circle:""},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1})]),_:2},1032,["onClick"])):T("",!0),l(i,{size:"small",type:"danger",onClick:g=>Dt(f),circle:""},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])])]),(r.type==="object"||r.type==="array")&&r.children?(b(),_("div",Vi,[Object.keys(r.children).length===0?(b(),_("div",Bi,[e[77]||(e[77]=d("span",null,"暂无子字段",-1)),l(i,{size:"small",type:"primary",onClick:g=>Ie(f)},{default:s(()=>e[76]||(e[76]=[w(" 添加子字段 ")])),_:2},1032,["onClick"])])):T("",!0),(b(!0),_(N,null,x(r.children,(g,V,F)=>(b(),_("div",{key:`child-${f}-${V}`,class:"child-field"},[d("div",ji,j(F+1),1),d("div",$i,[d("div",Oi,[l(a,{modelValue:g.name,"onUpdate:modelValue":k=>g.name=k,placeholder:"字段名称",size:"small",onChange:k=>Et(f,V,g.name)},null,8,["modelValue","onUpdate:modelValue","onChange"]),l(we,{modelValue:g.type,"onUpdate:modelValue":k=>g.type=k,placeholder:"选择类型",size:"small",onChange:k=>St(f,V,g.type)},{default:s(()=>[l(E,{label:"字符串",value:"string"}),l(E,{label:"数字",value:"number"}),l(E,{label:"布尔值",value:"boolean"}),l(E,{label:"对象",value:"object"}),l(E,{label:"数组",value:"array"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),d("div",ki,[l(i,{size:"small",type:"info",onClick:k=>qt(f,V),circle:"",disabled:F===0},{default:s(()=>[l(n,null,{default:s(()=>[l(y($e))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"info",onClick:k=>Gt(f,V),circle:"",disabled:F===Object.keys(r.children).length-1},{default:s(()=>[l(n,null,{default:s(()=>[l(y(pe))]),_:1})]),_:2},1032,["onClick","disabled"]),g.type==="object"||g.type==="array"?(b(),W(i,{key:0,size:"small",type:"primary",onClick:k=>Ke(f,V),circle:""},{default:s(()=>[l(n,null,{default:s(()=>[l(y(U))]),_:1})]),_:2},1032,["onClick"])):T("",!0),l(i,{size:"small",type:"danger",onClick:k=>Nt(f,V),circle:""},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])])]),(g.type==="object"||g.type==="array")&&g.children?(b(),_("div",Di,[Object.keys(g.children).length===0?(b(),_("div",Ei,[e[79]||(e[79]=d("span",null,"暂无嵌套字段",-1)),l(i,{size:"small",type:"primary",onClick:k=>Ke(f,V)},{default:s(()=>e[78]||(e[78]=[w(" 添加嵌套字段 ")])),_:2},1032,["onClick"])])):T("",!0),(b(!0),_(N,null,x(g.children,(k,ue,Je)=>(b(),_("div",{key:`nested-${f}-${V}-${ue}`,class:"nested-field"},[d("div",Si,j(Je+1),1),d("div",Ni,[d("div",xi,[l(a,{modelValue:k.name,"onUpdate:modelValue":Y=>k.name=Y,placeholder:"字段名称",size:"small",onChange:Y=>Jt(f,V,ue,k.name)},null,8,["modelValue","onUpdate:modelValue","onChange"]),l(we,{modelValue:k.type,"onUpdate:modelValue":Y=>k.type=Y,placeholder:"选择类型",size:"small",onChange:Y=>Ft(f,V,ue,k.type)},{default:s(()=>[l(E,{label:"字符串",value:"string"}),l(E,{label:"数字",value:"number"}),l(E,{label:"布尔值",value:"boolean"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),d("div",zi,[l(i,{size:"small",type:"info",onClick:Y=>Ht(f,V,ue),circle:"",disabled:Je===0},{default:s(()=>[l(n,null,{default:s(()=>[l(y($e))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"info",onClick:Y=>Qt(f,V,ue),circle:"",disabled:Je===Object.keys(g.children).length-1},{default:s(()=>[l(n,null,{default:s(()=>[l(y(pe))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"danger",onClick:Y=>Tt(f,V,ue),circle:""},{default:s(()=>[l(n,null,{default:s(()=>[l(y(M))]),_:1})]),_:2},1032,["onClick"])])])]))),128))])):T("",!0)]))),128))])):T("",!0)]))),128)),Object.keys(u).length===0?(b(),W(c,{key:0,description:"暂无字段结构","image-size":80},{default:s(()=>[l(i,{type:"primary",onClick:Ze},{default:s(()=>e[80]||(e[80]=[w(" 开始添加字段 ")])),_:1})]),_:1})):T("",!0)])])])])]),_:1},8,["modelValue"])])}}},Xi=el(Ji,[["__scopeId","data-v-9d885d47"]]);export{Xi as default};
