# Nukita PVV Project
基于Web的小说创作和阅读平台，集成了前后端功能。
前端使用vue3 语法使用 setup语法糖。
后端使用python实现具体逻辑，python的方法绑定到js中可以调用。
注意：每次修改我的代码的时候，注意不要遗漏了其他重要的代码片段。
## 项目结构
.
├── frontend/                        # Vue前端应用
│   ├── src/                        # 源代码目录
│   │   ├── assets/                 # 静态资源
│   │   ├── components/             # 组件
│   │   ├── composables/            # 组合式函数
│   │   ├── config/                 # 配置文件
│   │   ├── constants/              # 常量定义
│   │   ├── router/                 # 路由配置
│   │   ├── stores/                 # 状态管理
│   │   ├── utils/                  # 工具函数
│   │   ├── views/                  # 页面视图
│   │   │   ├── book/              # 写作相关页面
│   │   │   └── download/          # 下载相关页面
│   │   ├── App.vue                 # 根组件
│   │   ├── main.js                 # 入口文件
│   │   ├── binding.js              # 绑定配置
│   │   └── theme.css              # 主题样式
│   ├── public/                     # 公共资源
│   ├── index.html                  # HTML模板
│   ├── vite.config.js             # Vite配置
│   └── package.json               # 项目依赖配置
│
├── backend/                        # Python后端服务
│   ├── bridge/                    # 核心控制器
│   │   ├── API.py                 # API接口定义，对于BookController.py等等，会在里面实例化一个对象。
│   │   ├── Base.py                # 基础类
│   │   ├── BookController.py      # 书籍控制器
│   │   ├── ConfigManager.py       # 配置管理
│   │   ├── DrssionController.py   # Drssion控制器
│   │   ├── FeiShuController.py    # 飞书集成
│   │   ├── InjectProjectController.py # 项目注入控制器
│   │   ├── ModelController.py     # 模型控制器
│   │   ├── ProjectController.py   # 项目控制器
│   │   └── UserManager.py         # 用户管理
│   ├── models/                    # 暂时用不到
│   ├── pvvruntime/               # 运行时，不关注
│   ├── tools/                    # 工具集，不关注
│   └── dev.py                    # 不关注
│
├── statics/                      # 静态资源，自动生成不关注
├── webview/                      # Webview实现魔改代码，不关注
├── backup/                       # 备份文件夹，不关注，没有代码，配置文件的目录，和bridge下面相关的代码指定目录
├── out/                         # 构建输出
└── dev.py                       # 主开发入口

## 主要功能
1. 前端功能:
   - 小说下载界面
   - 写作界面
   - 主题定制
   - 状态管理
   - 路由系统
   - 等等

2. 后端功能:
   - API.py 作为前端绑调用js的绑定，调用逻辑可能在pywebview.api下面，也可能在 他的init实例化对象中，比如 pywebview.api.config_controller. 调用绑定的python方法这种。
   - 书籍管理
   - 用户管理
   - 项目配置
   - 飞书集成
   - 等等

## 开发说明
- 前端开发：基于Vue + Vite
- 后端开发：Python pywebview
- 开发环境启动：运行 dev.py