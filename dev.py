import re
import sys
import subprocess
import threading
import os
import platform



from backend.bridge.API import API

from backend.pvvruntime.Runtime import WebViewApp

vite_url = None
api = API()
DEFAULT_VITE_PORT = 13000  # Vite默认端口





def remove_ansi_escape_sequences(text):
    """ 移除 ANSI 转义字符 """
    ansi_escape_pattern = re.compile(r'\x1B\[[0-?9;]*[mK]')
    return ansi_escape_pattern.sub('', text)


def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        system = platform.system()
        if system == 'Windows':
            # Windows系统下查找并杀死进程
            find_cmd = f'netstat -ano | findstr :{port}'
            result = subprocess.check_output(find_cmd, shell=True).decode('gbk')  # 使用gbk编码解决Windows中文乱码
            if result:
                for line in result.splitlines():
                    if f':{port}' in line:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            pid = parts[4]
                            kill_cmd = f'taskkill /F /PID {pid}'
                            # 不打印系统返回消息，使用自定义消息
                            subprocess.call(kill_cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                            print(f"已终止占用端口 {port} 的进程 (PID: {pid})")
        elif system == 'Linux' or system == 'Darwin':  # Darwin是macOS
            # Linux/macOS下查找并杀死进程
            find_cmd = f"lsof -i :{port} | grep LISTEN"
            result = subprocess.check_output(find_cmd, shell=True).decode('utf-8')
            if result:
                pid = result.split()[1]
                kill_cmd = f"kill -9 {pid}"
                subprocess.call(kill_cmd, shell=True)
                print(f"已终止占用端口 {port} 的进程 (PID: {pid})")
        return True
    except Exception as e:
        print(f"尝试释放端口 {port} 时出错: {e}")
        return False


def run_process(command, cwd):
    """ 启动子进程并返回进程对象 """
    return subprocess.Popen(command, cwd=cwd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8',
                            text=True, shell=True)


def run_dev_mode():
    vite_path = os.path.join(os.path.abspath(os.pardir),"pvv", "frontend")
    print("正在启动Vite开发服务器...")
    print(vite_path)
    
    # 尝试释放默认Vite端口
    kill_process_on_port(DEFAULT_VITE_PORT)
    
    # 使用特定端口启动Vite
    return run_process(["pnpm", "run", "dev", "--", "--port", str(DEFAULT_VITE_PORT)], vite_path)


def run_build():

    vite_path = os.path.join( os.path.abspath(os.pardir),"pvv", "frontend")
    print(vite_path)
    print("正在构建项目...")
    result = subprocess.run(["pnpm", "run", "build", "--emptyOutDir"], cwd=vite_path, shell=True)

    if result.returncode == 0:
        print("构建成功完成。")
    else:
        print("构建失败。")





def read_output(process, url_found_event):
    """ 读取 Vite 进程的输出并查找 URL """
    global vite_url  # 使用全局变量
    while True:
        output = process.stdout.readline()
        if output == "" and process.poll() is not None:
            break
        if output:
            clean_output = remove_ansi_escape_sequences(output.strip())
            print(clean_output)  # 打印到控制台

            # 查找包含 URL 的行
            match = re.search(r'Local:\s+(http://\S+)', clean_output)
            if match:
                vite_url = match.group(1)  # 提取 URL
                print(f"Vite服务器运行于: {vite_url}")
                url_found_event.set()  # 通知主线程 URL 已找到


def start_webview(url, js_api):
    app = WebViewApp()
    app.run(url, js_api)


def cleanup(process):
    """清理资源，确保进程正确终止"""
    if process and process.poll() is None:
        try:
            process.terminate()
            process.wait(timeout=5)  # 等待进程终止
        except:
            process.kill()  # 如果正常终止失败，强制杀死
        print("已终止Vite进程")


def main():
    vite_process = None
    try:
        vite_process = run_dev_mode()
        url_found_event = threading.Event()

        output_thread = threading.Thread(target=read_output, args=(vite_process, url_found_event))
        output_thread.start()

        url_found_event.wait()  # 等待直到 URL 找到

        if vite_url:  # 使用全局变量
            start_webview(vite_url, js_api=api)
            print(f"Webview已启动，URL: {vite_url}")
        else:
            print("无法提取Vite URL。")
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 确保资源清理
        if vite_process:
            cleanup(vite_process)


if __name__ == '__main__':
    # 应用代理设置

    main()
