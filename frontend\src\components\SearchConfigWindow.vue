<template>
  <el-dialog
    v-model="dialogVisible"
    width="680px"
    :close-on-click-modal="false"
    :destroy-on-close="false"
    @closed="onDialogClosed"
    class="search-config-dialog no-select"
    :show-close="false"
  >
    <template #header="{ close }">
      <div class="dialog-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon><Search /></el-icon>
          </div>
          <div class="header-text">
            <h2 class="dialog-title">网络查询配置</h2>
            <p class="dialog-subtitle">管理您的搜索引擎和查询提供商</p>
          </div>
        </div>
        <el-button
          @click="close"
          class="close-btn"
          circle
          size="small"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <div class="search-config-container">
      <div class="provider-header">
        <div class="header-left">
          <h3 class="section-title no-select">查询提供商</h3>
          <span class="provider-count">{{ providers.length }} 个提供商</span>
        </div>
        <el-button type="primary" @click="addNewProvider" class="add-button">
          <el-icon><Plus /></el-icon>
          <span>添加提供商</span>
        </el-button>
      </div>

      <div class="provider-list-container">
        <div v-if="providers.length === 0" class="empty-state">
          <div class="empty-icon">
            <el-icon><Search /></el-icon>
          </div>
          <h4>暂无搜索提供商</h4>
          <p>添加您常用的搜索引擎，快速进行网络查询</p>
          <el-button type="primary" @click="addNewProvider" class="empty-add-btn">
            <el-icon><Plus /></el-icon>
            添加第一个提供商
          </el-button>
        </div>

        <div class="provider-cards" v-else>
          <div
            v-for="(provider, index) in providers"
            :key="provider.id || index"
            class="provider-item"
            :class="{ 'is-default': provider.isDefault }"
          >
            <div class="provider-content">
              <div class="provider-header-row">
                <div class="provider-info">
                  <div class="provider-name no-select">
                    {{ provider.name }}
                    <el-tag v-if="provider.isDefault" size="small" type="success" class="default-tag">
                      默认
                    </el-tag>
                  </div>
                  <div class="provider-url no-select">{{ formatUrl(provider.url) }}</div>
                </div>
                <div class="provider-actions">
                  <div class="action-buttons">
                    <el-tooltip content="设为默认" placement="top" v-if="!provider.isDefault">
                      <el-button
                        size="small"
                        @click="makeDefault(index)"
                        class="action-btn default-btn"
                      >
                        <el-icon><Star /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="上移" placement="top">
                      <el-button
                        size="small"
                        :disabled="index === 0"
                        @click="moveProvider(index, 'up')"
                        class="action-btn"
                      >
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="下移" placement="top">
                      <el-button
                        size="small"
                        :disabled="index === providers.length - 1"
                        @click="moveProvider(index, 'down')"
                        class="action-btn"
                      >
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="编辑" placement="top">
                      <el-button
                        size="small"
                        @click="editProvider(index)"
                        class="action-btn edit-btn"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="删除" placement="top">
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeProvider(index)"
                        class="action-btn delete-btn"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog
        v-model="editDialogVisible"
        width="600px"
        append-to-body
        class="provider-edit-dialog"
        :show-close="false"
      >
        <template #header="{ close }">
          <div class="edit-dialog-header">
            <div class="edit-header-content">
              <div class="edit-header-icon">
                <el-icon><Edit v-if="isEditing" /><Plus v-else /></el-icon>
              </div>
              <div class="edit-header-text">
                <h3 class="edit-dialog-title">{{ isEditing ? '编辑提供商' : '添加提供商' }}</h3>
                <p class="edit-dialog-subtitle">配置搜索引擎的名称和URL模板</p>
              </div>
            </div>
            <el-button
              @click="close"
              class="edit-close-btn"
              circle
              size="small"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </template>

        <el-form :model="currentProvider" label-width="120px" class="provider-form">
          <el-form-item label="提供商名称" required>
            <el-input
              v-model="currentProvider.name"
              placeholder="例如：Google、百度、知乎等"
              size="large"
            />
          </el-form-item>

          <el-form-item label="URL模板" required>
            <el-input
              v-model="currentProvider.url"
              placeholder="输入搜索引擎的URL地址"
              size="large"
            >
              <template #append>
                <el-tooltip placement="top" :hide-after="0">
                  <template #content>
                    <div class="url-help-content">
                      <h4>URL格式说明</h4>
                      <div class="help-section">
                        <p><strong>使用占位符（推荐）：</strong></p>
                        <ul>
                          <li>使用 <code>{query}</code> 或 <code>{q}</code> 作为搜索词占位符</li>
                          <li>搜索时会自动替换为实际的搜索内容</li>
                        </ul>
                      </div>
                      <div class="help-section">
                        <p><strong>不使用占位符：</strong></p>
                        <ul>
                          <li>直接输入网站URL，如 <code>https://www.google.com</code></li>
                          <li>点击打开时会复制搜索内容到剪贴板，方便手动粘贴</li>
                        </ul>
                      </div>
                    </div>
                  </template>
                  <el-button class="help-btn">
                    <el-icon><InfoFilled /></el-icon>
                  </el-button>
                </el-tooltip>
              </template>
            </el-input>

            <div class="url-examples">
              <div class="examples-header">
                <span class="examples-label">常用模板：</span>
              </div>
              <div class="examples-grid">
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.google.com/search?q={query}')"
                >
                  Google
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.baidu.com/s?wd={query}')"
                >
                  百度
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.bing.com/search?q={query}')"
                >
                  Bing
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.zhihu.com/search?q={query}')"
                >
                  知乎
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://github.com/search?q={query}')"
                >
                  GitHub
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://stackoverflow.com/search?q={query}')"
                >
                  Stack Overflow
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="设为默认">
            <el-switch
              v-model="currentProvider.isDefault"
              active-text="是"
              inactive-text="否"
            />
            <span class="switch-tip">默认提供商将在按回车键时使用</span>
          </el-form-item>

          <el-form-item label="URL预览">
            <div class="preview-container">
              <div class="preview-url">
                {{ previewUrl }}
              </div>
            </div>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="edit-dialog-footer">
            <el-button @click="editDialogVisible = false" size="large">
              取消
            </el-button>
            <el-button
              type="primary"
              @click="saveProvider"
              :disabled="!isProviderValid"
              size="large"
            >
              {{ isEditing ? '保存更改' : '添加提供商' }}
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Edit, Delete, InfoFilled, ArrowUp, ArrowDown, Plus, Search, Close, Star } from '@element-plus/icons-vue';
import { useConfigStore } from '@/stores/config';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bookId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'config-updated']);
const configStore = useConfigStore();

// Reactive variables
const dialogVisible = ref(false);
const editDialogVisible = ref(false);
const isEditing = ref(false);
const editIndex = ref(-1);
const providers = ref([]);

// Current provider being edited
const currentProvider = ref({
  id: '',
  name: '',
  url: '',
  isDefault: false
});

// Format URL for display (truncate if too long)
const formatUrl = (url) => {
  if (!url) return '';
  if (url.length > 50) {
    return url.substring(0, 47) + '...';
  }
  return url;
};

// Preview URL with example search term
const previewUrl = computed(() => {
  if (!currentProvider.value.url) return '输入URL后将显示预览效果';

  let url = currentProvider.value.url;
  const exampleQuery = '示例搜索词';

  if (url.includes('{query}')) {
    return url.replace('{query}', exampleQuery);
  } else if (url.includes('{q}')) {
    return url.replace('{q}', exampleQuery);
  } else {
    return url + ' (将复制搜索内容到剪贴板)';
  }
});

// Check if current provider is valid for saving
const isProviderValid = computed(() => {
  return (
    currentProvider.value.name.trim() !== '' && 
    currentProvider.value.url.trim() !== ''
    // 移除对{query}的强制检查，支持无占位符的URL
  );
});

// Watch for visibility change from parent component
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
  
  if (newValue) {
    // Load providers when dialog opens
    loadSearchConfig();
  }
});

// Watch for dialog visibility to update parent component
watch(dialogVisible, (newValue) => {
  emit('update:visible', newValue);
});

// Load search config from config store
const loadSearchConfig = () => {
  const searchConfig = configStore.search || { providers: [] };
  providers.value = JSON.parse(JSON.stringify(searchConfig.providers || []));
};

// Save search configuration
const saveConfig = async () => {
  try {
    // 确保至少一个默认提供商是设置
    if (providers.value.length > 0 && !providers.value.some(p => p.isDefault)) {
      providers.value[0].isDefault = true;
    }
    
    // 使用配置存储的updateConfig方法更新配置
    await configStore.updateConfig({
      search: {
        providers: providers.value
      }
    });
    
    emit('config-updated');
    ElMessage.success('搜索配置已保存');
    dialogVisible.value = false;
  } catch (error) {
    console.error('保存搜索配置失败:', error);
    ElMessage.error('保存配置失败: ' + error.message);
  }
};

// Add new provider
const addNewProvider = () => {
  isEditing.value = false;
  editIndex.value = -1;
  currentProvider.value = {
    id: generateId(),
    name: '',
    url: '',
    isDefault: providers.value.length === 0 // First provider is default
  };
  editDialogVisible.value = true;
};

// Edit existing provider
const editProvider = (index) => {
  isEditing.value = true;
  editIndex.value = index;
  currentProvider.value = JSON.parse(JSON.stringify(providers.value[index]));
  editDialogVisible.value = true;
};

// Remove provider
const removeProvider = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该搜索提供商吗？',
      '删除提供商',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const wasDefault = providers.value[index].isDefault;
    providers.value.splice(index, 1);
    
    // If the removed provider was default and we have other providers, set the first as default
    if (wasDefault && providers.value.length > 0) {
      providers.value[0].isDefault = true;
    }
  } catch {
    // User canceled the operation
  }
};

// Save provider (add new or update existing)
const saveProvider = () => {
  if (!isProviderValid.value) return;
  
  if (isEditing.value) {
    // Update existing provider
    providers.value[editIndex.value] = { ...currentProvider.value };
    
    // If this provider is set as default, unset all others
    if (currentProvider.value.isDefault) {
      makeDefault(editIndex.value);
    }
  } else {
    // Add new provider
    providers.value.push({ ...currentProvider.value });
    
    // If this provider is set as default, unset all others
    if (currentProvider.value.isDefault) {
      makeDefault(providers.value.length - 1);
    }
  }
  
  editDialogVisible.value = false;
};

// Set a provider as default and unset others
const makeDefault = (index) => {
  providers.value.forEach((provider, i) => {
    provider.isDefault = i === index;
  });
};

// 移动提供商位置 - 实现列表排序功能
const moveProvider = (index, direction) => {
  const newIndex = direction === 'up' ? index - 1 : index + 1;
  
  // 确保新位置在有效范围内
  if (newIndex < 0 || newIndex >= providers.value.length) {
    return;
  }
  
  // 交换元素位置
  const temp = providers.value[newIndex];
  providers.value[newIndex] = providers.value[index];
  providers.value[index] = temp;
  
  // 如果移动的是默认提供商，保持其默认状态
  if (providers.value[newIndex].isDefault) {
    makeDefault(newIndex);
  } else if (providers.value[index].isDefault) {
    makeDefault(index);
  }
};

// Generate a random ID for new providers
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Handle dialog closed
const onDialogClosed = () => {
  emit('update:visible', false);
};

// 将示例URL应用到当前提供商
const applyExample = (exampleUrl) => {
  currentProvider.value.url = exampleUrl;
};
</script>

<style lang="scss" scoped>
.search-config-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    background: var(--el-bg-color);
    max-height: 85vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__header) {
    padding: 0;
    margin: 0;
    flex-shrink: 0;
  }

  :deep(.el-dialog__body) {
    padding: 0;
    background: var(--el-bg-color);
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__footer) {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 16px 24px;
    background: var(--el-bg-color);
    flex-shrink: 0;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--el-color-primary);
  color: white;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        font-size: 24px;
        color: white;
      }
    }

    .header-text {
      .dialog-title {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: white;
      }

      .dialog-subtitle {
        margin: 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }

  .close-btn {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      color: white;
    }
  }
}

.search-config-container {
  flex: 1;
  padding: 24px;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: baseline;
    gap: 12px;

    .section-title {
      margin: 0;
      color: var(--el-text-color-primary);
      font-weight: 600;
      font-size: 18px;
    }

    .provider-count {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-light);
      padding: 2px 8px;
      border-radius: 12px;
    }
  }

  .add-button {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

.provider-list-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: var(--el-fill-color-blank);
  border-radius: 12px;
  border: 2px dashed var(--el-border-color);
  margin: 20px 0;

  .empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: var(--el-color-primary-light-9);
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      font-size: 36px;
      color: var(--el-color-primary);
    }
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: var(--el-text-color-primary);
    font-weight: 600;
  }

  p {
    margin: 0 0 24px 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  .empty-add-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
  }
}

.provider-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.provider-item {
  background: var(--el-fill-color-blank);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: var(--el-color-primary-light-5);
  }

  &.is-default {
    border-color: var(--el-color-success);
    background: var(--el-color-success-light-9);

    .provider-info .provider-name {
      color: var(--el-color-success-dark-2);
    }
  }
}

.provider-content {
  padding: 20px;
}

.provider-header-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;

  .provider-info {
    flex: 1;
    min-width: 0;

    .provider-name {
      font-weight: 600;
      font-size: 16px;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;

      .default-tag {
        font-weight: 500;
      }
    }

    .provider-url {
      color: var(--el-text-color-secondary);
      font-size: 13px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background: var(--el-fill-color);
      padding: 6px 10px;
      border-radius: 6px;
      word-break: break-all;
      border: 1px solid var(--el-border-color-lighter);
    }
  }

  .provider-actions {
    .action-buttons {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }
  }
}

.action-btn {
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid var(--el-border-color-light);

  &:hover {
    transform: translateY(-1px);
  }

  &.default-btn {
    color: var(--el-color-warning);
    border-color: var(--el-color-warning-light-7);

    &:hover {
      background: var(--el-color-warning-light-9);
      border-color: var(--el-color-warning-light-5);
    }
  }

  &.edit-btn {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary-light-7);

    &:hover {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-5);
    }
  }

  &.delete-btn {
    &:hover {
      transform: translateY(-1px);
    }
  }

  :deep(.el-icon) {
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 编辑对话框样式
.provider-edit-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    background: var(--el-bg-color);
  }

  :deep(.el-dialog__header) {
    padding: 0;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: var(--el-bg-color);
  }

  :deep(.el-dialog__footer) {
    padding: 0;
    margin: 0;
  }
}

.edit-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--el-color-success);
  color: white;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .edit-header-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .edit-header-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        font-size: 20px;
        color: white;
      }
    }

    .edit-header-text {
      .edit-dialog-title {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: white;
      }

      .edit-dialog-subtitle {
        margin: 0;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }

  .edit-close-btn {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      color: white;
    }
  }
}

.provider-form {
  :deep(.el-form-item__label) {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

.url-examples {
  margin-top: 12px;

  .examples-header {
    margin-bottom: 8px;

    .examples-label {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      font-weight: 500;
    }
  }

  .examples-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .example-tag {
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 6px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.help-btn {
  background: var(--el-color-info-light-8);
  border-color: var(--el-color-info-light-6);
  color: var(--el-color-info);

  &:hover {
    background: var(--el-color-info-light-7);
    border-color: var(--el-color-info-light-5);
  }
}

.url-help-content {
  max-width: 350px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .help-section {
    margin-bottom: 12px;

    p {
      margin: 0 0 6px 0;
      font-size: 13px;
    }

    ul {
      margin: 6px 0;
      padding-left: 16px;

      li {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.4;
      }
    }

    code {
      background: var(--el-fill-color-light);
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 11px;
    }
  }
}

.switch-tip {
  margin-left: 12px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.preview-container {
  .preview-url {
    background: var(--el-fill-color-blank);
    border: 1px solid var(--el-border-color-light);
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 13px;
    color: var(--el-text-color-regular);
    word-break: break-all;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    min-height: 20px;
  }
}

.edit-dialog-footer {
  padding: 16px 24px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 添加禁止选择文本的样式类
.no-select {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}
</style> 