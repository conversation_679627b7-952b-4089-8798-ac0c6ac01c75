<template>
  <el-dialog
    v-model="dialogVisible"
    title="网络查询配置"
    width="580px"
    :close-on-click-modal="false"
    :destroy-on-close="false"
    @closed="onDialogClosed"
    class="search-config-dialog no-select"
  >
    <div class="search-config-container">
      <div class="provider-list">
        <div class="provider-header">
          <h3 class="no-select">查询提供商</h3>
          <el-button type="primary" size="small" @click="addNewProvider" class="add-button">
            <el-icon><Plus /></el-icon>
            添加
          </el-button>
        </div>

        <el-empty v-if="providers.length === 0" description="暂无配置的搜索提供商" />

        <div class="provider-cards">
          <div 
            v-for="(provider, index) in providers" 
            :key="provider.id || index" 
            class="provider-item"
          >
            <div class="provider-content">
              <div class="provider-title-bar">
                <div class="provider-name no-select">{{ provider.name }}</div>
                <div class="provider-actions">
                  <el-switch 
                    v-model="provider.isDefault" 
                    @change="makeDefault(index)"
                    active-text="默认"
                    inactive-text=""
                    class="default-switch"
                  />
                  <div class="action-buttons">
                    <el-tooltip content="上移" placement="top" :hide-after="800">
                      <el-button 
                        circle 
                        :disabled="index === 0" 
                        @click="moveProvider(index, 'up')"
                        class="action-btn"
                      >
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="下移" placement="top" :hide-after="800">
                      <el-button 
                        circle 
                        :disabled="index === providers.length - 1" 
                        @click="moveProvider(index, 'down')"
                        class="action-btn"
                      >
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="编辑" placement="top" :hide-after="800">
                      <el-button 
                        circle 
                        @click="editProvider(index)"
                        class="action-btn"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="删除" placement="top" :hide-after="800">
                      <el-button 
                        circle 
                        type="danger" 
                        @click="removeProvider(index)"
                        class="action-btn"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div class="provider-url no-select">{{ provider.url }}</div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog
        v-model="editDialogVisible"
        :title="isEditing ? '编辑提供商' : '添加提供商'"
        width="500px"
        append-to-body
        class="provider-edit-dialog"
      >
        <el-form :model="currentProvider" label-width="100px">
          <el-form-item label="名称" required>
            <el-input v-model="currentProvider.name" placeholder="提供商名称" />
          </el-form-item>
          <el-form-item label="URL模板" required>
            <el-input 
              v-model="currentProvider.url" 
              placeholder="搜索引擎URL"
            >
              <template #append>
                <el-tooltip>
                  <template #content>
                    <div style="max-width: 300px;">
                      <p><strong>URL格式说明:</strong></p>
                      <p>1. 推荐使用 <code>{query}</code> 作为搜索词占位符</p>
                      <p>2. 如果不使用占位符，系统会自动在URL末尾添加搜索参数</p>
                      <p>3. 常见搜索引擎参数会自动识别：</p>
                      <ul style="padding-left: 15px; margin: 5px 0;">
                        <li>Google/Bing/知乎: <code>?q=搜索词</code></li>
                        <li>百度: <code>?wd=搜索词</code></li>
                        <li>搜狗: <code>?query=搜索词</code></li>
                        <li>B站: <code>?keyword=搜索词</code></li>
                      </ul>
                    </div>
                  </template>
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
            <div class="url-tip no-select">
              <span>例如：</span>
              <el-tag size="small" class="example-tag" @click="applyExample('https://www.baidu.com/s?wd={query}')">百度</el-tag>
              <el-tag size="small" class="example-tag" @click="applyExample('https://www.google.com/search?q={query}')">Google</el-tag>
              <el-tag size="small" class="example-tag" @click="applyExample('https://www.bing.com/search?q={query}')">Bing</el-tag>
              <el-tag size="small" class="example-tag" @click="applyExample('https://www.zhihu.com/search?q={query}')">知乎</el-tag>
            </div>
          </el-form-item>
          <el-form-item label="设为默认">
            <el-switch v-model="currentProvider.isDefault" />
          </el-form-item>
          <el-form-item label="预览">
            <div class="preview-url">
              {{ previewUrl }}
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveProvider" :disabled="!isProviderValid">
              保存
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Edit, Delete, InfoFilled, ArrowUp, ArrowDown, Plus } from '@element-plus/icons-vue';
import { useConfigStore } from '@/stores/config';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bookId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'config-updated']);
const configStore = useConfigStore();

// Reactive variables
const dialogVisible = ref(false);
const editDialogVisible = ref(false);
const isEditing = ref(false);
const editIndex = ref(-1);
const providers = ref([]);

// Current provider being edited
const currentProvider = ref({
  id: '',
  name: '',
  url: '',
  isDefault: false
});

// Preview URL with example search term
const previewUrl = computed(() => {
  if (!currentProvider.value.url) return '预览将在此显示';
  
  let url = currentProvider.value.url;
  const exampleQuery = '示例搜索词';
  
  if (url.includes('{query}')) {
    return url.replace('{query}', exampleQuery);
  } else if (url.includes('{q}')) {
    return url.replace('{q}', exampleQuery);
  } else {
    // 简单判断URL类型，添加适当的参数
    const lowerUrl = url.toLowerCase();
    
    if (lowerUrl.includes('baidu')) {
      // 百度
      return url + (url.includes('?') ? '&' : '?') + 'wd=' + exampleQuery;
    } else if (lowerUrl.includes('sogou')) {
      // 搜狗
      return url + (url.includes('?') ? '&' : '?') + 'query=' + exampleQuery;
    } else if (lowerUrl.includes('bilibili')) {
      // B站
      return url + (url.includes('?') ? '&' : '?') + 'keyword=' + exampleQuery;
    } else {
      // 默认使用q参数
      return url + (url.includes('?') ? '&' : '?') + 'q=' + exampleQuery;
    }
  }
});

// Check if current provider is valid for saving
const isProviderValid = computed(() => {
  return (
    currentProvider.value.name.trim() !== '' && 
    currentProvider.value.url.trim() !== ''
    // 移除对{query}的强制检查，支持无占位符的URL
  );
});

// Watch for visibility change from parent component
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
  
  if (newValue) {
    // Load providers when dialog opens
    loadSearchConfig();
  }
});

// Watch for dialog visibility to update parent component
watch(dialogVisible, (newValue) => {
  emit('update:visible', newValue);
});

// Load search config from config store
const loadSearchConfig = () => {
  const searchConfig = configStore.search || { providers: [] };
  providers.value = JSON.parse(JSON.stringify(searchConfig.providers || []));
};

// Save search configuration
const saveConfig = async () => {
  try {
    // 确保至少一个默认提供商是设置
    if (providers.value.length > 0 && !providers.value.some(p => p.isDefault)) {
      providers.value[0].isDefault = true;
    }
    
    // 使用配置存储的updateConfig方法更新配置
    await configStore.updateConfig({
      search: {
        providers: providers.value
      }
    });
    
    emit('config-updated');
    ElMessage.success('搜索配置已保存');
    dialogVisible.value = false;
  } catch (error) {
    console.error('保存搜索配置失败:', error);
    ElMessage.error('保存配置失败: ' + error.message);
  }
};

// Add new provider
const addNewProvider = () => {
  isEditing.value = false;
  editIndex.value = -1;
  currentProvider.value = {
    id: generateId(),
    name: '',
    url: '',
    isDefault: providers.value.length === 0 // First provider is default
  };
  editDialogVisible.value = true;
};

// Edit existing provider
const editProvider = (index) => {
  isEditing.value = true;
  editIndex.value = index;
  currentProvider.value = JSON.parse(JSON.stringify(providers.value[index]));
  editDialogVisible.value = true;
};

// Remove provider
const removeProvider = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该搜索提供商吗？',
      '删除提供商',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const wasDefault = providers.value[index].isDefault;
    providers.value.splice(index, 1);
    
    // If the removed provider was default and we have other providers, set the first as default
    if (wasDefault && providers.value.length > 0) {
      providers.value[0].isDefault = true;
    }
  } catch {
    // User canceled the operation
  }
};

// Save provider (add new or update existing)
const saveProvider = () => {
  if (!isProviderValid.value) return;
  
  if (isEditing.value) {
    // Update existing provider
    providers.value[editIndex.value] = { ...currentProvider.value };
    
    // If this provider is set as default, unset all others
    if (currentProvider.value.isDefault) {
      makeDefault(editIndex.value);
    }
  } else {
    // Add new provider
    providers.value.push({ ...currentProvider.value });
    
    // If this provider is set as default, unset all others
    if (currentProvider.value.isDefault) {
      makeDefault(providers.value.length - 1);
    }
  }
  
  editDialogVisible.value = false;
};

// Set a provider as default and unset others
const makeDefault = (index) => {
  providers.value.forEach((provider, i) => {
    provider.isDefault = i === index;
  });
};

// 移动提供商位置 - 实现列表排序功能
const moveProvider = (index, direction) => {
  const newIndex = direction === 'up' ? index - 1 : index + 1;
  
  // 确保新位置在有效范围内
  if (newIndex < 0 || newIndex >= providers.value.length) {
    return;
  }
  
  // 交换元素位置
  const temp = providers.value[newIndex];
  providers.value[newIndex] = providers.value[index];
  providers.value[index] = temp;
  
  // 如果移动的是默认提供商，保持其默认状态
  if (providers.value[newIndex].isDefault) {
    makeDefault(newIndex);
  } else if (providers.value[index].isDefault) {
    makeDefault(index);
  }
};

// Generate a random ID for new providers
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Handle dialog closed
const onDialogClosed = () => {
  emit('update:visible', false);
};

// 将示例URL应用到当前提供商
const applyExample = (exampleUrl) => {
  currentProvider.value.url = exampleUrl;
};
</script>

<style lang="scss" scoped>
.search-config-dialog {
  :deep(.el-dialog__header) {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 15px 20px;
    margin: 0;
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  :deep(.el-dialog__footer) {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 12px 20px;
  }
}

.search-config-container {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h3 {
    margin: 0;
    color: var(--el-text-color-primary);
    font-weight: 500;
  }
  
  .add-button {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.provider-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.provider-item {
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color-lighter);
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.provider-content {
  padding: 12px 16px;
}

.provider-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  
  .provider-name {
    font-weight: 600;
    font-size: 16px;
    color: var(--el-text-color-primary);
  }
  
  .provider-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .default-switch {
      margin-right: 4px;
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.action-btn {
  padding: 6px;
  height: 28px;
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  :deep(.el-icon) {
    font-size: 14px;
  }
}

.provider-url {
  color: var(--el-text-color-secondary);
  font-size: 13px;
  word-break: break-all;
  background-color: var(--el-fill-color);
  padding: 8px 10px;
  border-radius: 4px;
  margin-top: 8px;
}

.url-tip {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  
  .example-tag {
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

.preview-url {
  background-color: var(--el-fill-color-light);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  color: var(--el-text-color-regular);
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

// 添加禁止选择文本的样式类
.no-select {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.provider-edit-dialog {
  :deep(.el-form-item__label) {
    font-weight: 500;
  }
}
</style> 